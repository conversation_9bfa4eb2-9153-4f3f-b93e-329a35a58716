import { computed, inject } from '@angular/core';
import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of, debounceTime, distinctUntilChanged } from 'rxjs';
import { Coach, CoachFilters, SortOption } from '../models/coach.model';
import { CoachesService } from '../services/coaches.service';

export interface CoachesState {
  coaches: Coach[];
  filteredCoaches: Coach[];
  selectedCoach: Coach | null;
  filters: CoachFilters;
  sortOption: SortOption;
  loading: boolean;
  error: string | null;
  
  // Pagination
  currentPage: number;
  pageSize: number;
  hasMoreData: boolean;
  
  // UI State
  showFilterModal: boolean;
  searchQuery: string;
}

const initialState: CoachesState = {
  coaches: [],
  filteredCoaches: [],
  selectedCoach: null,
  filters: {
    showFavoritesOnly: false
  },
  sortOption: 'rating-desc',
  loading: false,
  error: null,
  currentPage: 0,
  pageSize: 20,
  hasMoreData: true,
  showFilterModal: false,
  searchQuery: ''
};

export const CoachesStore = signalStore(
  { providedIn: 'root' },
  withState<CoachesState>(initialState),
  withComputed(({ coaches, filters, sortOption, searchQuery }) => ({
    // Filter coaches based on current filters and search
    filteredAndSortedCoaches: computed(() => {
      let result = [...coaches()];
      const currentFilters = filters();
      const query = searchQuery().toLowerCase().trim();

      // Apply search filter
      if (query) {
        result = result.filter(coach => 
          coach.name.toLowerCase().includes(query) ||
          coach.title?.toLowerCase().includes(query) ||
          coach.bio?.toLowerCase().includes(query)
        );
      }

      // Apply favorites filter
      if (currentFilters.showFavoritesOnly) {
        result = result.filter(coach => coach.isFavorite);
      }

      // Apply target goals filter
      if (currentFilters.targetGoals?.length) {
        result = result.filter(coach =>
          coach.targetGoals.some(goal => 
            currentFilters.targetGoals!.includes(goal.id)
          )
        );
      }

      // Apply environments filter
      if (currentFilters.environments?.length) {
        result = result.filter(coach =>
          coach.environments.some(env => 
            currentFilters.environments!.includes(env.id)
          )
        );
      }

      // Apply special considerations filter
      if (currentFilters.specialConsiderations?.length) {
        result = result.filter(coach =>
          coach.specialConsiderations.some(consideration => 
            currentFilters.specialConsiderations!.includes(consideration.id)
          )
        );
      }

      // Apply rating filter
      if (currentFilters.rating) {
        result = result.filter(coach => {
          const rating = coach.rating;
          return rating >= currentFilters.rating!.min && 
                 (!currentFilters.rating!.max || rating <= currentFilters.rating!.max);
        });
      }

      // Apply sorting
      const sort = sortOption();
      result.sort((a, b) => {
        switch (sort) {
          case 'rating-asc':
            return a.rating - b.rating;
          case 'rating-desc':
            return b.rating - a.rating;
          case 'name-asc':
            return a.name.localeCompare(b.name);
          case 'name-desc':
            return b.name.localeCompare(a.name);
          case 'experience-asc':
            return (a.experience || 0) - (b.experience || 0);
          case 'experience-desc':
            return (b.experience || 0) - (a.experience || 0);
          default:
            return 0;
        }
      });

      return result;
    }),

    // Get active filter count for UI
    activeFilterCount: computed(() => {
      const currentFilters = filters();
      let count = 0;
      
      if (currentFilters.targetGoals?.length) count += currentFilters.targetGoals.length;
      if (currentFilters.environments?.length) count += currentFilters.environments.length;
      if (currentFilters.specialConsiderations?.length) count += currentFilters.specialConsiderations.length;
      if (currentFilters.experienceLevel?.length) count += currentFilters.experienceLevel.length;
      if (currentFilters.specialties?.length) count += currentFilters.specialties.length;
      if (currentFilters.rating) count += 1;
      if (currentFilters.priceRange) count += 1;
      if (currentFilters.languages?.length) count += currentFilters.languages.length;
      if (currentFilters.showFavoritesOnly) count += 1;
      
      return count;
    }),

    // Get favorite coaches
    favoriteCoaches: computed(() => 
      coaches().filter(coach => coach.isFavorite)
    )
  })),
  withMethods((store, coachesService = inject(CoachesService)) => ({
    // Load coaches
    loadCoaches: rxMethod<void>(
      pipe(
        tap(() => patchState(store, { loading: true, error: null })),
        switchMap(() =>
          coachesService.getCoaches().pipe(
            tap(coaches => patchState(store, { 
              coaches, 
              loading: false,
              currentPage: 1 
            })),
            catchError(error => {
              patchState(store, { 
                loading: false, 
                error: error.message || 'Failed to load coaches' 
              });
              return of([]);
            })
          )
        )
      )
    ),

    // Load more coaches (infinite scroll)
    loadMoreCoaches: rxMethod<void>(
      pipe(
        tap(() => patchState(store, { loading: true })),
        switchMap(() => {
          const currentPage = store.currentPage();
          const pageSize = store.pageSize();
          
          return coachesService.getCoaches(currentPage + 1, pageSize).pipe(
            tap(newCoaches => {
              const existingCoaches = store.coaches();
              patchState(store, { 
                coaches: [...existingCoaches, ...newCoaches],
                currentPage: currentPage + 1,
                hasMoreData: newCoaches.length === pageSize,
                loading: false
              });
            }),
            catchError(error => {
              patchState(store, { 
                loading: false, 
                error: error.message || 'Failed to load more coaches' 
              });
              return of([]);
            })
          );
        })
      )
    ),

    // Search coaches
    searchCoaches: rxMethod<string>(
      pipe(
        debounceTime(300),
        distinctUntilChanged(),
        tap(query => patchState(store, { searchQuery: query }))
      )
    ),

    // Set filters
    setFilters: (filters: Partial<CoachFilters>) => {
      patchState(store, { 
        filters: { ...store.filters(), ...filters }
      });
    },

    // Reset filters
    resetFilters: () => {
      patchState(store, { 
        filters: { showFavoritesOnly: false },
        searchQuery: ''
      });
    },

    // Set sort option
    setSortOption: (sortOption: SortOption) => {
      patchState(store, { sortOption });
    },

    // Toggle favorite
    toggleFavorite: (coachId: string) => {
      const coaches = store.coaches().map(coach => 
        coach.id === coachId 
          ? { ...coach, isFavorite: !coach.isFavorite }
          : coach
      );
      patchState(store, { coaches });
    },

    // Select coach
    selectCoach: (coach: Coach | null) => {
      patchState(store, { selectedCoach: coach });
    },

    // UI methods
    showFilterModal: () => {
      patchState(store, { showFilterModal: true });
    },

    hideFilterModal: () => {
      patchState(store, { showFilterModal: false });
    },

    // Clear error
    clearError: () => {
      patchState(store, { error: null });
    }
  }))
);
