import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { toSignal } from '@angular/core/rxjs-interop';
import { switchMap, of } from 'rxjs';

import { CoachesService } from '../../services/coaches.service';
import { Coach } from '../../models/coach.model';

@Component({
  selector: 'lib-coach-details',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  templateUrl: './coach-details.component.html',
  styleUrl: './coach-details.component.scss'
})
export class CoachDetailsComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly coachesService = inject(CoachesService);

  // Get coach data from route params
  coach = toSignal(
    this.route.params.pipe(
      switchMap(params => {
        const coachId = params['id'];
        return coachId ? this.coachesService.getCoachById(coachId) : of(null);
      })
    )
  );

  ngOnInit() {
    // Component initialization
  }

  onBackClick() {
    this.router.navigate(['/coaches']);
  }

  onBookSession() {
    // TODO: Implement booking functionality
    console.log('Book session with coach:', this.coach()?.name);
  }

  onMessageCoach() {
    // TODO: Implement messaging functionality
    console.log('Message coach:', this.coach()?.name);
  }

  onViewReviews() {
    const coachId = this.coach()?.id;
    if (coachId) {
      this.router.navigate(['/coaches', coachId, 'reviews']);
    }
  }
}
