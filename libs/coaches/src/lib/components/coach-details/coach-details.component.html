<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onBackClick()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Coach Details</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="coach-details-content">
  <div *ngIf="coach(); else loadingTemplate" class="coach-details">
    
    <!-- Coach Header -->
    <div class="coach-header">
      <div class="coach-avatar">
        <img [src]="coach()!.avatar" [alt]="coach()!.name" />
      </div>
      <div class="coach-info">
        <h1>{{ coach()!.name }}</h1>
        <div class="coach-rating">
          <ion-icon name="star" class="star-icon"></ion-icon>
          <span>{{ coach()!.rating }}</span>
          <span class="review-count">({{ coach()!.reviewCount }} reviews)</span>
        </div>
        <div class="coach-location" *ngIf="coach()!.location">
          <ion-icon name="location-outline"></ion-icon>
          <span>{{ coach()!.location }}</span>
        </div>
      </div>
    </div>

    <!-- Coach Stats -->
    <div class="coach-stats">
      <div class="stat-item">
        <span class="stat-value">{{ coach()!.experience }}</span>
        <span class="stat-label">Years Experience</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{ coach()!.sessionCount || 0 }}</span>
        <span class="stat-label">Sessions</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">${{ coach()!.pricePerSession }}</span>
        <span class="stat-label">Per Session</span>
      </div>
    </div>

    <!-- Bio -->
    <div class="coach-section" *ngIf="coach()!.bio">
      <h2>About</h2>
      <p>{{ coach()!.bio }}</p>
    </div>

    <!-- Specialties -->
    <div class="coach-section" *ngIf="coach()!.specialties?.length">
      <h2>Specialties</h2>
      <div class="specialties-grid">
        <ion-chip *ngFor="let specialty of coach()!.specialties" class="specialty-chip">
          {{ specialty }}
        </ion-chip>
      </div>
    </div>

    <!-- Target Goals -->
    <div class="coach-section" *ngIf="coach()!.targetGoals?.length">
      <h2>Target Goals</h2>
      <div class="goals-grid">
        <ion-chip *ngFor="let goal of coach()!.targetGoals" class="goal-chip">
          {{ goal }}
        </ion-chip>
      </div>
    </div>

    <!-- Certifications -->
    <div class="coach-section" *ngIf="coach()!.certifications?.length">
      <h2>Certifications</h2>
      <div class="certifications-list">
        <div *ngFor="let cert of coach()!.certifications" class="certification-item">
          <h3>{{ cert.name }}</h3>
          <p>{{ cert.organization }}</p>
          <span class="cert-year">{{ cert.year }}</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <ion-button expand="block" (click)="onBookSession()" class="book-button">
        Book Session
      </ion-button>
      <ion-button expand="block" fill="outline" (click)="onMessageCoach()" class="message-button">
        Message Coach
      </ion-button>
      <ion-button expand="block" fill="clear" (click)="onViewReviews()" class="reviews-button">
        View Reviews ({{ coach()!.reviewCount }})
      </ion-button>
    </div>
  </div>

  <ng-template #loadingTemplate>
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading coach details...</p>
    </div>
  </ng-template>
</ion-content>
