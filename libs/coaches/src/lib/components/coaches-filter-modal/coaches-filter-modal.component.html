<ion-header>
  <ion-toolbar>
    <ion-title>Filters</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="filter-content">
  <!-- Favorites Toggle -->
  <div class="filter-section">
    <div class="section-header">
      <h3>Favorites</h3>
    </div>
    <div class="section-content">
      <ion-item lines="none" class="toggle-item">
        <ion-label>Show favorites only</ion-label>
        <ion-toggle 
          [checked]="localFilters.showFavoritesOnly"
          (ionChange)="toggleFavoritesOnly()"
          slot="end">
        </ion-toggle>
      </ion-item>
    </div>
  </div>

  <!-- Sort Options -->
  <div class="filter-section">
    <div class="section-header">
      <h3>Sort by</h3>
    </div>
    <div class="section-content">
      <ion-radio-group [(ngModel)]="sortOption" (ionChange)="onSortChange()">
        <ion-item lines="none">
          <ion-radio slot="start" value="rating-desc"></ion-radio>
          <ion-label>Reviews (High to Low)</ion-label>
        </ion-item>
        <ion-item lines="none">
          <ion-radio slot="start" value="rating-asc"></ion-radio>
          <ion-label>Reviews (Low to High)</ion-label>
        </ion-item>
        <ion-item lines="none">
          <ion-radio slot="start" value="name-asc"></ion-radio>
          <ion-label>Name (A to Z)</ion-label>
        </ion-item>
        <ion-item lines="none">
          <ion-radio slot="start" value="name-desc"></ion-radio>
          <ion-label>Name (Z to A)</ion-label>
        </ion-item>
        <ion-item lines="none">
          <ion-radio slot="start" value="experience-desc"></ion-radio>
          <ion-label>Experience (High to Low)</ion-label>
        </ion-item>
      </ion-radio-group>
    </div>
  </div>

  <!-- Rating Filter -->
  <div class="filter-section">
    <div class="section-header">
      <h3>Rating</h3>
    </div>
    <div class="section-content">
      <div class="rating-filter">
        <div class="rating-range">
          <label>Minimum Rating: {{ minRating }}/10</label>
          <ion-range
            [(ngModel)]="minRating"
            min="0"
            max="10"
            step="0.5"
            snaps="true"
            (ionChange)="onRatingChange()"
            class="rating-slider">
            <div slot="start">0</div>
            <div slot="end">10</div>
          </ion-range>
        </div>
      </div>
    </div>
  </div>

  <!-- Price Range Filter -->
  <div class="filter-section">
    <div class="section-header">
      <h3>Price Range</h3>
    </div>
    <div class="section-content">
      <ion-item lines="none" class="toggle-item">
        <ion-label>Enable price filter</ion-label>
        <ion-toggle 
          [(ngModel)]="priceEnabled"
          (ionChange)="onPriceChange()"
          slot="end">
        </ion-toggle>
      </ion-item>
      
      <div *ngIf="priceEnabled" class="price-range">
        <label>Price Range: ${{ minPrice }} - ${{ maxPrice }}</label>
        <ion-range
          [(ngModel)]="minPrice"
          min="0"
          max="200"
          step="10"
          snaps="true"
          (ionChange)="onPriceChange()"
          class="price-slider">
          <div slot="start">$0</div>
          <div slot="end">$200+</div>
        </ion-range>
      </div>
    </div>
  </div>

  <!-- Collapsible Filter Sections -->
  <div *ngFor="let section of filterSections" class="filter-section collapsible">
    <div class="section-header" (click)="toggleSection(section.id)">
      <h3>{{ section.title }}</h3>
      <ion-icon 
        [name]="section.expanded ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="section.expanded">
      <div class="options-grid">
        <div 
          *ngFor="let option of section.options"
          class="option-item"
          (click)="toggleOption(section.id, option.id)">
          <ion-icon 
            [name]="option.selected ? 'checkbox' : 'checkbox-outline'"
            [class.selected]="option.selected"
            class="checkbox-icon">
          </ion-icon>
          <span class="option-label">{{ option.name }}</span>
        </div>
      </div>
    </div>
  </div>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <div class="footer-actions">
      <ion-button 
        fill="clear" 
        color="medium"
        (click)="resetFilters()"
        class="reset-button">
        Reset All
      </ion-button>
      
      <ion-button 
        fill="solid" 
        color="primary"
        (click)="applyFilters()"
        class="apply-button">
        Apply Filters
        <ion-badge *ngIf="getActiveFilterCount() > 0" class="filter-count">
          {{ getActiveFilterCount() }}
        </ion-badge>
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
