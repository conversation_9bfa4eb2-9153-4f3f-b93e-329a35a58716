ion-header {
  ion-toolbar {
    --background: var(--fitness-bg-secondary);
    --color: var(--fitness-text-primary);
    --border-color: var(--fitness-border);

    ion-title {
      font-weight: 600;
    }

    ion-button {
      --color: var(--fitness-text-primary);
    }
  }
}

.filter-content {
  --background: var(--fitness-bg-primary);
  --color: var(--fitness-text-primary);
}

.filter-section {
  margin-bottom: 24px;
  border-bottom: 1px solid var(--fitness-border);
  padding-bottom: 16px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 12px 16px;
    cursor: pointer;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--fitness-text-primary);
      margin: 0;
    }

    .expand-icon {
      font-size: 20px;
      color: var(--fitness-text-secondary);
      transition: transform 0.2s ease;
    }
  }

  .section-content {
    padding: 0 16px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:not(.expanded) {
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
    }

    &.expanded {
      max-height: 1000px;
    }
  }

  &:not(.collapsible) .section-content {
    max-height: none !important;
    padding: 0 16px;
  }
}

.toggle-item {
  --background: transparent;
  --color: var(--fitness-text-primary);
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;

  ion-toggle {
    --background: var(--fitness-bg-secondary);
    --background-checked: var(--fitness-accent);
    --handle-background: var(--fitness-text-primary);
    --handle-background-checked: var(--fitness-bg-primary);
  }
}

ion-radio-group {
  ion-item {
    --background: transparent;
    --color: var(--fitness-text-primary);
    --padding-start: 0;
    --padding-end: 0;
    --inner-padding-end: 0;
    margin-bottom: 8px;

    ion-radio {
      --color: var(--fitness-accent);
      --color-checked: var(--fitness-accent);
      margin-right: 12px;
    }

    ion-label {
      font-size: 16px;
    }
  }
}

.rating-filter,
.price-range {
  .rating-range,
  .price-range {
    margin-top: 16px;

    label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--fitness-text-primary);
      margin-bottom: 12px;
    }

    ion-range {
      --bar-background: var(--fitness-bg-secondary);
      --bar-background-active: var(--fitness-accent);
      --knob-background: var(--fitness-accent);
      --knob-border-radius: 50%;
      --knob-size: 20px;
      --pin-background: var(--fitness-accent);
      --pin-color: var(--fitness-bg-primary);

      div[slot] {
        font-size: 12px;
        color: var(--fitness-text-secondary);
      }
    }
  }
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 8px;

  .option-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--fitness-bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--fitness-border);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--fitness-overlay);
      transform: translateY(-1px);
    }

    .checkbox-icon {
      font-size: 20px;
      color: var(--fitness-text-secondary);
      transition: color 0.2s ease;

      &.selected {
        color: var(--fitness-accent);
      }
    }

    .option-label {
      font-size: 14px;
      color: var(--fitness-text-primary);
      font-weight: 500;
    }
  }
}

ion-footer {
  ion-toolbar {
    --background: var(--fitness-bg-secondary);
    --border-color: var(--fitness-border);
    padding: 8px 0;

    .footer-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;
      gap: 12px;

      .reset-button {
        --color: var(--fitness-text-secondary);
        --background: transparent;
        flex: 0 0 auto;
      }

      .apply-button {
        --background: var(--fitness-accent);
        --color: var(--fitness-bg-primary);
        --border-radius: 12px;
        flex: 1;
        position: relative;

        .filter-count {
          position: absolute;
          top: -4px;
          right: -4px;
          background: var(--fitness-bg-primary);
          color: var(--fitness-accent);
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          border-radius: 8px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .options-grid {
    .option-item {
      padding: 10px;

      .option-label {
        font-size: 13px;
      }
    }
  }

  .filter-section .section-header h3 {
    font-size: 16px;
  }
}

// Dark theme adjustments
:host-context(body.theme-fitness) {
  ion-header ion-toolbar {
    --background: var(--fitness-bg-secondary);
  }

  .filter-content {
    --background: var(--fitness-bg-primary);
  }

  ion-footer ion-toolbar {
    --background: var(--fitness-bg-secondary);
  }
}
