import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { addIcons } from 'ionicons';
import {
  closeOutline,
  chevronDownOutline,
  chevronUpOutline,
  checkboxOutline,
  checkbox,
  starOutline,
  star
} from 'ionicons/icons';

import { CoachesStore } from '../../state/coaches.store';
import {
  CoachFilters,
  TARGET_GOALS,
  ENVIRONMENTS,
  SPECIAL_CONSIDERATIONS,
  EXPERIENCE_LEVELS,
  SortOption
} from '../../models/coach.model';

interface FilterSection {
  id: string;
  title: string;
  expanded: boolean;
  options: FilterOption[];
}

interface FilterOption {
  id: string;
  name: string;
  selected: boolean;
}

@Component({
  selector: 'lib-coaches-filter-modal',
  imports: [CommonModule, IonicModule, FormsModule],
  templateUrl: './coaches-filter-modal.component.html',
  styleUrl: './coaches-filter-modal.component.scss',
})
export class CoachesFilterModalComponent implements OnInit {
  private readonly modalController = inject(ModalController);
  private readonly coachesStore = inject(CoachesStore);

  // Local filter state
  localFilters: CoachFilters = {};
  sortOption: SortOption = 'rating-desc';

  // Filter sections
  filterSections: FilterSection[] = [];

  // Rating filter
  minRating = 0;
  maxRating = 10;

  // Price range filter
  minPrice = 0;
  maxPrice = 200;
  priceEnabled = false;

  constructor() {
    addIcons({
      closeOutline,
      chevronDownOutline,
      chevronUpOutline,
      checkboxOutline,
      checkbox,
      starOutline,
      star
    });
  }

  ngOnInit() {
    this.initializeFilters();
    this.setupFilterSections();
  }

  private initializeFilters() {
    const currentFilters = this.coachesStore.filters();
    const currentSort = this.coachesStore.sortOption();

    this.localFilters = { ...currentFilters };
    this.sortOption = currentSort;

    // Initialize rating filter
    if (currentFilters.rating) {
      this.minRating = currentFilters.rating.min;
      this.maxRating = currentFilters.rating.max || 10;
    }

    // Initialize price filter
    if (currentFilters.priceRange) {
      this.minPrice = currentFilters.priceRange.min;
      this.maxPrice = currentFilters.priceRange.max;
      this.priceEnabled = true;
    }
  }

  private setupFilterSections() {
    this.filterSections = [
      {
        id: 'targetGoals',
        title: 'Target Goals',
        expanded: true,
        options: TARGET_GOALS.map(goal => ({
          id: goal.id,
          name: goal.name,
          selected: this.localFilters.targetGoals?.includes(goal.id) || false
        }))
      },
      {
        id: 'environments',
        title: 'Location/Environment',
        expanded: false,
        options: ENVIRONMENTS.map(env => ({
          id: env.id,
          name: env.name,
          selected: this.localFilters.environments?.includes(env.id) || false
        }))
      },
      {
        id: 'specialConsiderations',
        title: 'Special Considerations',
        expanded: false,
        options: SPECIAL_CONSIDERATIONS.map(consideration => ({
          id: consideration.id,
          name: consideration.name,
          selected: this.localFilters.specialConsiderations?.includes(consideration.id) || false
        }))
      },
      {
        id: 'experienceLevel',
        title: 'Experience Level',
        expanded: false,
        options: EXPERIENCE_LEVELS.map(level => ({
          id: level,
          name: level,
          selected: this.localFilters.experienceLevel?.includes(level) || false
        }))
      }
    ];
  }

  toggleSection(sectionId: string) {
    const section = this.filterSections.find(s => s.id === sectionId);
    if (section) {
      section.expanded = !section.expanded;
    }
  }

  toggleOption(sectionId: string, optionId: string) {
    const section = this.filterSections.find(s => s.id === sectionId);
    if (!section) return;

    const option = section.options.find(o => o.id === optionId);
    if (!option) return;

    option.selected = !option.selected;
    this.updateLocalFilters(sectionId);
  }

  private updateLocalFilters(sectionId: string) {
    const section = this.filterSections.find(s => s.id === sectionId);
    if (!section) return;

    const selectedOptions = section.options
      .filter(option => option.selected)
      .map(option => option.id);

    switch (sectionId) {
      case 'targetGoals':
        this.localFilters.targetGoals = selectedOptions.length ? selectedOptions : undefined;
        break;
      case 'environments':
        this.localFilters.environments = selectedOptions.length ? selectedOptions : undefined;
        break;
      case 'specialConsiderations':
        this.localFilters.specialConsiderations = selectedOptions.length ? selectedOptions : undefined;
        break;
      case 'experienceLevel':
        this.localFilters.experienceLevel = selectedOptions.length ? selectedOptions as any : undefined;
        break;
    }
  }

  onRatingChange() {
    if (this.minRating > 0 || this.maxRating < 10) {
      this.localFilters.rating = {
        min: this.minRating,
        max: this.maxRating
      };
    } else {
      this.localFilters.rating = undefined;
    }
  }

  onPriceChange() {
    if (this.priceEnabled) {
      this.localFilters.priceRange = {
        min: this.minPrice,
        max: this.maxPrice
      };
    } else {
      this.localFilters.priceRange = undefined;
    }
  }

  toggleFavoritesOnly() {
    this.localFilters.showFavoritesOnly = !this.localFilters.showFavoritesOnly;
  }

  onSortChange() {
    // Sort option is handled separately
  }

  applyFilters() {
    this.coachesStore.setFilters(this.localFilters);
    this.coachesStore.setSortOption(this.sortOption);
    this.modalController.dismiss({ filtersApplied: true });
  }

  resetFilters() {
    this.localFilters = { showFavoritesOnly: false };
    this.sortOption = 'rating-desc';
    this.minRating = 0;
    this.maxRating = 10;
    this.minPrice = 0;
    this.maxPrice = 200;
    this.priceEnabled = false;

    // Reset all filter sections
    this.filterSections.forEach(section => {
      section.options.forEach(option => {
        option.selected = false;
      });
    });
  }

  closeModal() {
    this.modalController.dismiss();
  }

  getActiveFilterCount(): number {
    let count = 0;

    if (this.localFilters.targetGoals?.length) count += this.localFilters.targetGoals.length;
    if (this.localFilters.environments?.length) count += this.localFilters.environments.length;
    if (this.localFilters.specialConsiderations?.length) count += this.localFilters.specialConsiderations.length;
    if (this.localFilters.experienceLevel?.length) count += this.localFilters.experienceLevel.length;
    if (this.localFilters.rating) count += 1;
    if (this.localFilters.priceRange) count += 1;
    if (this.localFilters.showFavoritesOnly) count += 1;

    return count;
  }

  getRatingStars(rating: number): boolean[] {
    const stars: boolean[] = [];
    for (let i = 1; i <= 10; i++) {
      stars.push(i <= rating);
    }
    return stars;
  }
}
