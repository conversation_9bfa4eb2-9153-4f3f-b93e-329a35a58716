<ion-card class="coach-card" button="true">
  <div class="card-content">
    <!-- Coach <PERSON><PERSON> and Basic Info -->
    <div class="coach-header">
      <div class="avatar-container">
        <img
          [src]="coach().avatar"
          [alt]="coach().name"
          class="coach-avatar"
          loading="lazy">
        <ion-badge
          *ngIf="coach().isPro"
          class="pro-badge">
          PRO
        </ion-badge>
      </div>

      <div class="coach-info">
        <div class="name-rating">
          <h3 class="coach-name">{{ coach().name }}</h3>
          <ion-button
            fill="clear"
            size="small"
            class="favorite-button"
            (click)="onFavoriteClick($event)">
            <ion-icon
              [name]="coach().isFavorite ? 'heart' : 'heart-outline'"
              [class.favorited]="coach().isFavorite">
            </ion-icon>
          </ion-button>
        </div>

        <p *ngIf="coach().title" class="coach-title">{{ coach().title }}</p>

        <!-- Rating -->
        <div class="rating-container">
          <div class="stars">
            <ion-icon
              *ngFor="let filled of getStarArray(coach().rating); let i = index"
              [name]="filled ? 'star' : 'star-outline'"
              [class.filled]="filled">
            </ion-icon>
          </div>
          <span class="rating-text">{{ coach().rating }}/10</span>
          <span class="review-count">({{ coach().reviewCount }} reviews)</span>
        </div>
      </div>
    </div>

    <!-- Target Goals Chips -->
    <div class="goals-container" *ngIf="coach().targetGoals?.length">
      <div class="goals-chips">
        <ion-chip
          *ngFor="let goal of getDisplayedGoals(coach().targetGoals)"
          class="goal-chip">
          {{ goal.name }}
          <ion-badge
            *ngIf="goal.level"
            class="level-badge">
            {{ goal.level }}
          </ion-badge>
        </ion-chip>

        <ion-chip
          *ngIf="hasMoreGoals(coach().targetGoals)"
          class="more-goals-chip">
          +{{ getMoreGoalsCount(coach().targetGoals) }} more
        </ion-chip>
      </div>
    </div>

    <!-- Experience and Certifications -->
    <div class="additional-info">
      <div class="info-item" *ngIf="coach().experience">
        <ion-icon name="checkmark-circle" class="info-icon"></ion-icon>
        <span>{{ coach().experience }} years experience</span>
      </div>

      <div class="info-item" *ngIf="coach().certifications?.length">
        <ion-icon name="checkmark-circle" class="info-icon"></ion-icon>
        <span>{{ coach().certifications.length }} certifications</span>
      </div>
    </div>

    <!-- Bio Preview -->
    <p *ngIf="coach().bio" class="coach-bio">
      {{ coach().bio && coach().bio.length > 100 ? (coach().bio | slice:0:100) + '...' : coach().bio }}
    </p>
  </div>
</ion-card>
