.coach-card {
  --background: var(--fitness-bg-secondary);
  --color: var(--fitness-text-primary);
  margin: 8px 0;
  border-radius: 16px;
  box-shadow: var(--fitness-shadow);
  border: 1px solid var(--fitness-border);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 8px 20px -4px rgba(0, 0, 0, 0.15), 0px 20px 25px -5px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

.card-content {
  padding: 16px;
}

.coach-header {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;

  .avatar-container {
    position: relative;
    flex-shrink: 0;

    .coach-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--fitness-border);
    }

    .pro-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      background: var(--fitness-accent);
      color: var(--fitness-bg-primary);
      font-size: 8px;
      font-weight: 700;
      padding: 2px 4px;
      border-radius: 6px;
      min-width: auto;
    }
  }

  .coach-info {
    flex: 1;
    min-width: 0; // Prevent overflow

    .name-rating {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 4px;

      .coach-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--fitness-text-primary);
        margin: 0;
        line-height: 1.2;
      }

      .favorite-button {
        --color: var(--fitness-text-secondary);
        --background: transparent;
        --padding-start: 4px;
        --padding-end: 4px;
        margin: 0;
        min-height: auto;

        ion-icon {
          font-size: 20px;
          transition: all 0.2s ease;

          &.favorited {
            --color: #ff4757;
            color: #ff4757;
          }
        }

        &:hover ion-icon {
          transform: scale(1.1);
        }
      }
    }

    .coach-title {
      font-size: 14px;
      color: var(--fitness-text-secondary);
      margin: 0 0 8px 0;
      line-height: 1.2;
    }

    .rating-container {
      display: flex;
      align-items: center;
      gap: 6px;

      .stars {
        display: flex;
        gap: 2px;

        ion-icon {
          font-size: 14px;
          color: var(--fitness-text-secondary);

          &.filled {
            color: var(--fitness-accent);
          }
        }
      }

      .rating-text {
        font-size: 14px;
        font-weight: 600;
        color: var(--fitness-text-primary);
      }

      .review-count {
        font-size: 12px;
        color: var(--fitness-text-secondary);
      }
    }
  }
}

.goals-container {
  margin-bottom: 12px;

  .goals-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .goal-chip {
      --background: var(--fitness-overlay);
      --color: var(--fitness-text-primary);
      font-size: 12px;
      height: 28px;
      border-radius: 14px;
      position: relative;

      .level-badge {
        background: var(--fitness-accent);
        color: var(--fitness-bg-primary);
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
        margin-left: 4px;
      }
    }

    .more-goals-chip {
      --background: transparent;
      --color: var(--fitness-text-secondary);
      border: 1px solid var(--fitness-border);
      font-size: 12px;
      height: 28px;
      border-radius: 14px;
    }
  }
}

.additional-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;

  .info-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--fitness-text-secondary);

    .info-icon {
      font-size: 14px;
      color: var(--fitness-accent);
    }
  }
}

.coach-bio {
  font-size: 14px;
  color: var(--fitness-text-secondary);
  line-height: 1.4;
  margin: 0;
}

// Responsive adjustments
@media (max-width: 480px) {
  .coach-header {
    .avatar-container .coach-avatar {
      width: 50px;
      height: 50px;
    }

    .coach-info .name-rating .coach-name {
      font-size: 16px;
    }
  }

  .goals-container .goals-chips {
    .goal-chip,
    .more-goals-chip {
      font-size: 11px;
      height: 26px;
    }
  }
}

// Dark theme specific adjustments
:host-context(body.theme-fitness) {
  .coach-card {
    --background: var(--fitness-bg-secondary);
    border-color: var(--fitness-border);
  }
}
