<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onBackClick()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Reviews</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="reviews-content">
  <div *ngIf="coach()" class="coach-header">
    <div class="coach-info">
      <img [src]="coach()!.avatar" [alt]="coach()!.name" class="coach-avatar" />
      <div class="coach-details">
        <h2>{{ coach()!.name }}</h2>
        <div class="coach-rating">
          <ion-icon name="star" class="star-icon"></ion-icon>
          <span>{{ coach()!.rating }}</span>
          <span class="review-count">({{ coach()!.reviewCount }} reviews)</span>
        </div>
      </div>
    </div>
  </div>

  <div class="reviews-container">
    <div *ngIf="reviews() && reviews()!.length > 0; else noReviewsTemplate" class="reviews-list">
      <div *ngFor="let review of reviews()" class="review-item">
        <div class="review-header">
          <div class="reviewer-info">
            <div class="reviewer-avatar">
              <span>{{ review.reviewerName.charAt(0).toUpperCase() }}</span>
            </div>
            <div class="reviewer-details">
              <h3>{{ review.reviewerName }}</h3>
              <p class="review-date">{{ formatDate(review.date) }}</p>
            </div>
          </div>
          <div class="review-rating">
            <div class="stars">
              <ion-icon 
                *ngFor="let filled of getStarArray(review.rating)"
                [name]="filled ? 'star' : 'star-outline'"
                [class.filled]="filled">
              </ion-icon>
            </div>
          </div>
        </div>
        
        <div class="review-content">
          <p>{{ review.comment }}</p>
        </div>

        <div *ngIf="review.helpfulCount > 0" class="review-helpful">
          <ion-icon name="thumbs-up-outline"></ion-icon>
          <span>{{ review.helpfulCount }} found this helpful</span>
        </div>
      </div>
    </div>

    <ng-template #noReviewsTemplate>
      <div class="no-reviews">
        <ion-icon name="chatbubbles-outline" class="no-reviews-icon"></ion-icon>
        <h3>No Reviews Yet</h3>
        <p>This coach hasn't received any reviews yet. Be the first to leave a review!</p>
      </div>
    </ng-template>
  </div>
</ion-content>
