import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, delay } from 'rxjs';
import { Coach, CoachReview } from '../models/coach.model';
import { API_URL } from '@nutrition/shared';

@Injectable({
  providedIn: 'root'
})
export class CoachesService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = inject(API_URL);

  getCoaches(page: number = 1, pageSize: number = 20): Observable<Coach[]> {
    // For now, return mock data. Replace with actual API call later
    return this.getMockCoaches().pipe(delay(500));
    
    // Actual API call would be:
    // return this.http.get<Coach[]>(`${this.apiUrl}/coaches`, {
    //   params: { page: page.toString(), pageSize: pageSize.toString() }
    // });
  }

  getCoachById(id: string): Observable<Coach | null> {
    // Mock implementation
    return this.getMockCoaches().pipe(
      delay(300),
      // In real implementation, filter by ID or make separate API call
    );
    
    // Actual API call would be:
    // return this.http.get<Coach>(`${this.apiUrl}/coaches/${id}`);
  }

  getCoachReviews(coachId: string, page: number = 1, pageSize: number = 10): Observable<CoachReview[]> {
    // Mock implementation
    return of(this.getMockReviews()).pipe(delay(300));
    
    // Actual API call would be:
    // return this.http.get<CoachReview[]>(`${this.apiUrl}/coaches/${coachId}/reviews`, {
    //   params: { page: page.toString(), pageSize: pageSize.toString() }
    // });
  }

  addReview(coachId: string, review: Omit<CoachReview, 'id' | 'date'>): Observable<CoachReview> {
    // Mock implementation
    const newReview: CoachReview = {
      ...review,
      id: Date.now().toString(),
      date: new Date()
    };
    return of(newReview).pipe(delay(500));
    
    // Actual API call would be:
    // return this.http.post<CoachReview>(`${this.apiUrl}/coaches/${coachId}/reviews`, review);
  }

  updateReview(coachId: string, reviewId: string, review: Partial<CoachReview>): Observable<CoachReview> {
    // Mock implementation
    const updatedReview: CoachReview = {
      id: reviewId,
      userId: 'current-user',
      userName: 'Current User',
      rating: review.rating || 5,
      comment: review.comment || '',
      date: new Date(),
      isEditable: true
    };
    return of(updatedReview).pipe(delay(500));
    
    // Actual API call would be:
    // return this.http.put<CoachReview>(`${this.apiUrl}/coaches/${coachId}/reviews/${reviewId}`, review);
  }

  toggleFavorite(coachId: string): Observable<boolean> {
    // Mock implementation
    return of(true).pipe(delay(300));
    
    // Actual API call would be:
    // return this.http.post<{isFavorite: boolean}>(`${this.apiUrl}/coaches/${coachId}/favorite`, {})
    //   .pipe(map(response => response.isFavorite));
  }

  private getMockCoaches(): Observable<Coach[]> {
    const mockCoaches: Coach[] = [
      {
        id: '1',
        name: 'Sarah M.',
        title: 'Certified Personal Trainer',
        avatar: 'https://static.motiffcontent.com/private/resource/image/197d1db92b5f69f-23d664c4-058b-4862-9ed1-91593a6e576b.jpeg',
        rating: 9.5,
        reviewCount: 124,
        bio: 'Professional strength coach specialized in powerlifting and functional training with 10+ years of experience helping clients achieve their fitness goals. Certified in advanced training techniques and nutrition planning.',
        motto: 'Your only limit is the one you set yourself',
        experience: 10,
        isPro: true,
        isFavorite: true,
        targetGoals: [
          { id: 'muscle-gain', name: 'Strength', level: 5 },
          { id: 'endurance', name: 'Power', level: 4 },
          { id: 'flexibility', name: 'Endurance', level: 3 }
        ],
        specialties: [
          { id: 'weight-loss', name: 'Weight Loss' },
          { id: 'muscle-gain', name: 'Muscle Gain' },
          { id: 'rehabilitation', name: 'Rehabilitation' },
          { id: 'flexibility', name: 'Flexibility' }
        ],
        certifications: [
          { id: '1', name: 'NASM Certified', verified: true },
          { id: '2', name: 'CrossFit Level 2', verified: true },
          { id: '3', name: 'First Aid Certified', verified: true }
        ],
        environments: [
          { id: 'gym', name: 'Gym' },
          { id: 'home', name: 'Home' }
        ],
        specialConsiderations: [
          { id: 'injury-safe', name: 'Injury-safe' }
        ],
        gallery: [
          'https://static.motiffcontent.com/private/resource/image/197d1db92b69668-b481d555-070e-4dfb-a62b-f570dd570b31.jpeg',
          'https://static.motiffcontent.com/private/resource/image/197d1db92b683da-4b0bf837-6ea0-43b6-8461-c1c30536a1fa.jpeg',
          'https://static.motiffcontent.com/private/resource/image/197d1db92b790bd-239ffc06-9fdd-42e3-b992-7ec1b2b7e1cd.jpeg',
          'https://static.motiffcontent.com/private/resource/image/197d1db92b82968-cbb95c8c-c9c4-4f6f-b0ae-0136a41b2a51.jpeg'
        ],
        workouts: [
          {
            id: '1',
            title: 'Strength Training',
            description: 'Full body strength workout',
            duration: 45,
            difficulty: 'Intermediate',
            thumbnail: 'https://static.motiffcontent.com/private/resource/image/197d1db92b69668-b481d555-070e-4dfb-a62b-f570dd570b31.jpeg'
          },
          {
            id: '2',
            title: 'HIIT Workout',
            description: 'High intensity interval training',
            duration: 30,
            difficulty: 'Advanced',
            thumbnail: 'https://static.motiffcontent.com/private/resource/image/197d1db92b683da-4b0bf837-6ea0-43b6-8461-c1c30536a1fa.jpeg'
          }
        ]
      },
      {
        id: '2',
        name: 'John D.',
        title: 'Fitness Specialist',
        avatar: 'https://static.motiffcontent.com/private/resource/image/197ccb28013e2a7-7c4b5928-11fb-453b-a510-db9067991bc3.jpeg',
        rating: 8.5,
        reviewCount: 89,
        bio: 'Experienced fitness trainer specializing in functional movement and injury prevention.',
        experience: 7,
        isPro: false,
        isFavorite: false,
        targetGoals: [
          { id: 'muscle-gain', name: 'Strength', level: 4 },
          { id: 'endurance', name: 'Power', level: 3 },
          { id: 'flexibility', name: 'Endurance', level: 2 }
        ],
        specialties: [
          { id: 'rehabilitation', name: 'Rehabilitation' },
          { id: 'flexibility', name: 'Flexibility' }
        ],
        certifications: [
          { id: '4', name: 'ACE Certified', verified: true }
        ],
        environments: [
          { id: 'gym', name: 'Gym' },
          { id: 'outdoor', name: 'Outdoor' }
        ],
        specialConsiderations: [
          { id: 'senior-friendly', name: 'Senior-friendly' }
        ],
        gallery: []
      },
      {
        id: '3',
        name: 'Emily R.',
        title: 'Yoga & Wellness Coach',
        avatar: 'https://static.motiffcontent.com/private/resource/image/197ccb28013e2a7-7c4b5928-11fb-453b-a510-db9067991bc3.jpeg',
        rating: 10.0,
        reviewCount: 156,
        bio: 'Certified yoga instructor and wellness coach focused on mind-body connection.',
        experience: 8,
        isPro: true,
        isFavorite: true,
        targetGoals: [
          { id: 'flexibility', name: 'Strength', level: 3 },
          { id: 'relaxation', name: 'Power', level: 5 },
          { id: 'posture', name: 'Endurance', level: 4 }
        ],
        specialties: [
          { id: 'flexibility', name: 'Flexibility' },
          { id: 'relaxation', name: 'Relaxation' }
        ],
        certifications: [
          { id: '5', name: 'RYT 500', verified: true },
          { id: '6', name: 'Meditation Teacher', verified: true }
        ],
        environments: [
          { id: 'home', name: 'Home' },
          { id: 'office', name: 'Office' }
        ],
        specialConsiderations: [
          { id: 'pregnancy-safe', name: 'Pregnancy-safe' },
          { id: 'desk-friendly', name: 'Desk-friendly' }
        ],
        gallery: []
      }
    ];

    return of(mockCoaches);
  }

  private getMockReviews(): CoachReview[] {
    return [
      {
        id: '1',
        userId: 'user1',
        userName: 'Alex K.',
        userAvatar: 'https://static.motiffcontent.com/private/resource/image/197d1db92b98e47-156a3a41-0682-4d96-b10a-c3f4022e94c2.jpeg',
        rating: 5,
        comment: 'Sarah helped me achieve my fitness goals in just 3 months. Her personalized approach made all the difference!',
        date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
        isEditable: false
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'Jessica T.',
        userAvatar: 'https://static.motiffcontent.com/private/resource/image/197d1db92b9650e-3144cdc5-81f4-4516-9799-7f7e70751824.jpeg',
        rating: 5,
        comment: 'The best trainer I\'ve ever worked with. Knowledgeable, motivating and truly cares about her clients\' progress.',
        date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 1 month ago
        isEditable: false
      }
    ];
  }
}
