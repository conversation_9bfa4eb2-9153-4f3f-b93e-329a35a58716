import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, IonContent, ModalController, InfiniteScrollCustomEvent } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { addIcons } from 'ionicons';
import {
  searchOutline,
  optionsOutline,
  heartOutline,
  heart,
  starOutline,
  star,
  locationOutline
} from 'ionicons/icons';

import { CoachesStore } from '../state/coaches.store';
import { GlobalStore } from '@nutrition/shared';
import { CoachCardComponent } from '../components/coach-card/coach-card.component';
import { CoachesFilterModalComponent } from '../components/coaches-filter-modal/coaches-filter-modal.component';
import { Coach, TARGET_GOALS } from '../models/coach.model';

@Component({
  selector: 'lib-coaches',
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    RouterLink,
    CoachCardComponent
  ],
  templateUrl: './coaches.component.html',
  styleUrl: './coaches.component.scss',
})
export class CoachesComponent implements OnInit {
  @ViewChild(IonContent, { static: false }) content!: IonContent;

  private readonly coachesStore = inject(CoachesStore);
  private readonly globalStore = inject(GlobalStore);
  private readonly modalController = inject(ModalController);

  // Expose store signals to template
  coaches = this.coachesStore.filteredAndSortedCoaches;
  loading = this.coachesStore.loading;
  error = this.coachesStore.error;
  hasMoreData = this.coachesStore.hasMoreData;
  activeFilterCount = this.coachesStore.activeFilterCount;
  searchQuery = this.coachesStore.searchQuery;

  // Quick filter options (target goals)
  quickFilters = TARGET_GOALS.slice(0, 4); // Show first 4 as quick filters
  selectedQuickFilters: string[] = [];

  constructor() {
    addIcons({
      searchOutline,
      optionsOutline,
      heartOutline,
      heart,
      starOutline,
      star,
      locationOutline
    });
  }

  ngOnInit() {
    this.globalStore.setTitle('Coaches');
    this.globalStore.setTheme('Fitness');
    this.loadCoaches();
  }

  ionViewWillEnter() {
    this.globalStore.setTitle('Coaches');
    this.globalStore.setTheme('Fitness');
  }

  loadCoaches() {
    this.coachesStore.loadCoaches();
  }

  onSearchChange(event: any) {
    const query = event.detail.value || '';
    this.coachesStore.searchCoaches(query);
  }

  onQuickFilterToggle(filterId: string) {
    const currentFilters = this.coachesStore.filters();
    const targetGoals = currentFilters.targetGoals || [];

    let updatedTargetGoals: string[];
    if (targetGoals.includes(filterId)) {
      updatedTargetGoals = targetGoals.filter(id => id !== filterId);
      this.selectedQuickFilters = this.selectedQuickFilters.filter(id => id !== filterId);
    } else {
      updatedTargetGoals = [...targetGoals, filterId];
      this.selectedQuickFilters = [...this.selectedQuickFilters, filterId];
    }

    this.coachesStore.setFilters({ targetGoals: updatedTargetGoals });
  }

  isQuickFilterSelected(filterId: string): boolean {
    const currentFilters = this.coachesStore.filters();
    return currentFilters.targetGoals?.includes(filterId) || false;
  }

  async openFilterModal() {
    const modal = await this.modalController.create({
      component: CoachesFilterModalComponent,
      presentingElement: await this.modalController.getTop(),
      canDismiss: true,
      showBackdrop: true
    });

    await modal.present();

    const { data } = await modal.onWillDismiss();
    if (data?.filtersApplied) {
      // Filters were applied in the modal, coaches list will update automatically
      this.scrollToTop();
    }
  }

  onCoachCardClick(coach: Coach) {
    this.coachesStore.selectCoach(coach);
    // Navigate to coach details - will be implemented with routing
  }

  onFavoriteToggle(coach: Coach) {
    this.coachesStore.toggleFavorite(coach.id);
  }

  onInfiniteScroll(event: InfiniteScrollCustomEvent) {
    if (this.hasMoreData() && !this.loading()) {
      this.coachesStore.loadMoreCoaches();
    }

    setTimeout(() => {
      event.target.complete();
    }, 500);
  }

  private scrollToTop() {
    this.content?.scrollToTop(300);
  }

  trackByCoachId(index: number, coach: Coach): string {
    return coach.id;
  }
}
