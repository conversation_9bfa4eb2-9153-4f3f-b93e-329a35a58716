<ion-content [fullscreen]="true" class="coaches-content">
  <!-- Header with search and filter -->
  <div class="coaches-header">
    <div class="header-top">
      <h1 class="page-title">Coaches</h1>
      <ion-button
        fill="clear"
        class="filter-button"
        (click)="openFilterModal()">
        <ion-icon name="options-outline" slot="icon-only"></ion-icon>
        <ion-badge
          *ngIf="activeFilterCount() > 0"
          class="filter-badge">
          {{ activeFilterCount() }}
        </ion-badge>
      </ion-button>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <ion-searchbar
        [value]="searchQuery()"
        placeholder="Search coaches..."
        (ionInput)="onSearchChange($event)"
        class="coaches-searchbar">
      </ion-searchbar>
      <ion-button
        fill="solid"
        color="primary"
        class="filter-fab"
        (click)="openFilterModal()">
        <ion-icon name="options-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </div>

    <!-- Quick Filters (Target Goals) -->
    <div class="quick-filters">
      <ion-chip
        *ngFor="let filter of quickFilters"
        [class.selected]="isQuickFilterSelected(filter.id)"
        (click)="onQuickFilterToggle(filter.id)"
        class="quick-filter-chip">
        {{ filter.name }}
      </ion-chip>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading() && coaches().length === 0" class="loading-container">
    <ion-spinner name="crescent" color="primary"></ion-spinner>
    <p>Loading coaches...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error()" class="error-container">
    <ion-icon name="alert-circle-outline" color="danger"></ion-icon>
    <p>{{ error() }}</p>
    <ion-button fill="outline" (click)="loadCoaches()">
      Try Again
    </ion-button>
  </div>

  <!-- Coaches List -->
  <div *ngIf="!loading() || coaches().length > 0" class="coaches-list">
    <div class="coaches-grid">
      <lib-coach-card
        *ngFor="let coach of coaches()"
        [coach]="coach"
        (click)="onCoachCardClick(coach)"
        (favoriteToggle)="onFavoriteToggle(coach)">
      </lib-coach-card>
    </div>

    <!-- Infinite Scroll -->
    <ion-infinite-scroll
      threshold="100px"
      position="bottom"
      [disabled]="!hasMoreData() || loading()"
      (ionInfinite)="onInfiniteScroll($event)">
      <ion-infinite-scroll-content
        loadingSpinner="crescent"
        loadingText="Loading more coaches...">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading() && coaches().length === 0 && !error()" class="empty-state">
    <ion-icon name="people-outline" color="medium"></ion-icon>
    <h3>No coaches found</h3>
    <p>Try adjusting your search or filters to find more coaches.</p>
    <ion-button fill="outline" (click)="coachesStore.resetFilters()">
      Clear Filters
    </ion-button>
  </div>
</ion-content>
