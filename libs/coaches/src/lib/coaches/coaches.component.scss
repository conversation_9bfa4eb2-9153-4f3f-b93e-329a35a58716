.coaches-content {
  --background: var(--fitness-bg-primary);
  --color: var(--fitness-text-primary);
}

.coaches-header {
  padding: 16px;
  background: var(--fitness-bg-primary);
  border-bottom: 1px solid var(--fitness-border);

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .page-title {
      font-size: 28px;
      font-weight: 700;
      color: var(--fitness-text-primary);
      margin: 0;
    }

    .filter-button {
      position: relative;
      --color: var(--fitness-text-primary);
      --background: transparent;

      .filter-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: var(--fitness-accent);
        color: var(--fitness-bg-primary);
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
      }
    }
  }

  .search-container {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 16px;

    .coaches-searchbar {
      flex: 1;
      --background: var(--fitness-bg-secondary);
      --color: var(--fitness-text-primary);
      --placeholder-color: var(--fitness-text-secondary);
      --icon-color: var(--fitness-text-secondary);
      --border-radius: 12px;
      --box-shadow: none;
      --padding-start: 16px;
      --padding-end: 16px;
    }

    .filter-fab {
      --background: var(--fitness-accent);
      --color: var(--fitness-bg-primary);
      --border-radius: 12px;
      width: 48px;
      height: 48px;
      margin: 0;
    }
  }

  .quick-filters {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;

    &::-webkit-scrollbar {
      display: none;
    }

    .quick-filter-chip {
      --background: var(--fitness-bg-secondary);
      --color: var(--fitness-text-secondary);
      border: 1px solid var(--fitness-border);
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
      cursor: pointer;
      transition: all 0.2s ease;

      &.selected {
        --background: var(--fitness-accent);
        --color: var(--fitness-bg-primary);
        border-color: var(--fitness-accent);
      }

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}

.coaches-list {
  padding: 0 16px;

  .coaches-virtual-scroll {
    height: calc(100vh - 280px); // Adjust based on header height
  }
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;

  ion-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  h3 {
    color: var(--fitness-text-primary);
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    color: var(--fitness-text-secondary);
    font-size: 16px;
    margin: 0 0 24px 0;
    max-width: 300px;
  }

  ion-button {
    --border-color: var(--fitness-accent);
    --color: var(--fitness-accent);
  }
}

.loading-container {
  ion-spinner {
    --color: var(--fitness-accent);
    margin-bottom: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .coaches-header {
    .search-container {
      .filter-fab {
        display: none; // Hide on mobile, use header button instead
      }
    }
  }
}

// Dark theme adjustments for fitness theme
:host-context(body.theme-fitness) {
  .coaches-content {
    --background: var(--fitness-bg-primary);
  }
}
