<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <properties>
    <springdoc-openapi-starter-webmvc-ui.version>2.4.0</springdoc-openapi-starter-webmvc-ui.version>
    <webdrivermanager.version>5.8.0</webdrivermanager.version>
    <selenium-java.version>4.21.0</selenium-java.version>
    <webmagic.version>0.10.3</webmagic.version>
    <scala.binary.version>2.13</scala.binary.version>
    <jjwt.version>0.12.6</jjwt.version>
  </properties>
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.3.0</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <packaging>pom</packaging>

  <groupId>com.nutrition</groupId>
  <artifactId>nutrition-master</artifactId>
  <version>1.0-SNAPSHOT</version>

  <modules>
    <module>backend/nutrition-master/nutrition-web</module>
    <module>backend/nutrition-master/libs/tazz-crawler</module>
    <module>backend/nutrition-master/libs/eatntrack-crawler</module>
    <module>backend/nutrition-master/libs/selenium-shared</module>
    <module>backend/nutrition-master/libs/products</module>
    <module>backend/nutrition-master/libs/shared</module>
    <module>backend/nutrition-master/libs/shared-domain</module>
    <module>backend/nutrition-master/libs/shared-service</module>
    <module>backend/nutrition-master/libs/shared-web</module>
    <module>backend/nutrition-master/libs/nutrition</module>
    <module>backend/nutrition-master/libs/jobs</module>
    <module>backend/nutrition-master/libs/security</module>
    <module>backend/nutrition-master/libs/test</module>
    <module>backend/nutrition-master/libs/playground</module>
    <module>backend/nutrition-master/libs/user-profile</module>
    <module>backend/nutrition-master/libs/localization</module>
    <module>backend/nutrition-master/libs/concepts</module>
    <module>backend/nutrition-master/libs/questionnaire</module>
    <module>backend/nutrition-master/libs/video</module>
    <module>backend/nutrition-master/libs/photo</module>
    <module>backend/nutrition-master/libs/coaches</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <!--poms-->
        <groupId>com.typesafe.akka</groupId>
        <artifactId>akka-bom_${scala.binary.version}</artifactId>
        <version>2.9.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-all</artifactId>
        <version>4.0.24</version>
        <type>pom</type>
      </dependency>
      <!--project-->
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>security</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>tazz-crawler</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>eatntrack-crawler</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>selenium-shared</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>shared</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>products</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>nutrition</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>test</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>jobs</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>shared-web</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>shared-service</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>shared-domain</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>localization</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>user-profile</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>questionnaire</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>concepts</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>video</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>photo</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>coaches</artifactId>
        <version>${version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.bonigarcia</groupId>
        <artifactId>webdrivermanager</artifactId>
        <version>${webdrivermanager.version}</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-java</artifactId>
        <version>${selenium-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>5.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>5.2.3</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jjwt.version}</version>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-module-junit4</artifactId>
        <version>2.0.9</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-api-mockito2</artifactId>
        <version>2.0.9</version>
        <scope>test</scope>
      </dependency>
      <!-- https://mvnrepository.com/artifact/org.assertj/assertj-core -->
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.26.3</version>
      </dependency>
      <dependency>
        <groupId>io.hypersistence</groupId>
        <artifactId>hypersistence-utils-hibernate-63</artifactId>
        <version>3.9.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <repositories>
    <repository>
      <id>akka-repository</id>
      <name>Akka library repository</name>
      <url>https://repo.akka.io/maven</url>
    </repository>
  </repositories>
</project>
