package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.MutableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * CoachWorkout entity representing workouts created by coaches
 */
@Entity
@Table(name = "coach_workout", indexes = {
    @Index(name = "idx_coach_workout_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_workout_title", columnList = "title"),
    @Index(name = "idx_coach_workout_difficulty", columnList = "difficulty")
})
public class CoachWorkout extends MutableEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "duration")
    private Integer duration; // Duration in minutes

    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty", length = 20)
    private WorkoutDifficulty difficulty;

    @Column(name = "thumbnail", length = 500)
    private String thumbnail;

    @Column(name = "video_url", length = 500)
    private String videoUrl;

    // Constructors
    public CoachWorkout() {}

    public CoachWorkout(Coach coach, String title) {
        this.coach = coach;
        this.title = title;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public WorkoutDifficulty getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(WorkoutDifficulty difficulty) {
        this.difficulty = difficulty;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    /**
     * Enum representing workout difficulty levels
     */
    public enum WorkoutDifficulty {
        BEGINNER("Beginner"),
        INTERMEDIATE("Intermediate"),
        ADVANCED("Advanced"),
        EXPERT("Expert"),
        PROFESSIONAL("Professional");

        private final String displayName;

        WorkoutDifficulty(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
