package com.nutrition.libs.coaches.controller;

import com.nutrition.libs.coaches.service.CoachService;
import com.nutrition.libs.coaches.service.dto.CoachDTO;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import com.nutrition.libs.coaches.service.dto.CoachReviewDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Coach operations with lazy loading and filtering support
 */
@RestController
@RequestMapping("/api/coaches")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CoachController {

    private static final Logger log = LoggerFactory.getLogger(CoachController.class);

    private final CoachService coachService;

    public CoachController(CoachService coachService) {
        this.coachService = coachService;
    }

    /**
     * GET /api/coaches : Get all coaches with optional filtering and pagination
     */
    @GetMapping
    public ResponseEntity<Page<CoachDTO>> getAllCoaches(
            @RequestParam(value = "search", required = false) String searchQuery,
            @RequestParam(value = "targetGoals", required = false) List<String> targetGoals,
            @RequestParam(value = "specialties", required = false) List<String> specialties,
            @RequestParam(value = "environments", required = false) List<String> environments,
            @RequestParam(value = "specialConsiderations", required = false) List<String> specialConsiderations,
            @RequestParam(value = "minExperience", required = false) Integer minExperience,
            @RequestParam(value = "maxExperience", required = false) Integer maxExperience,
            @RequestParam(value = "minRating", required = false) BigDecimal minRating,
            @RequestParam(value = "maxRating", required = false) BigDecimal maxRating,
            @RequestParam(value = "minPrice", required = false) BigDecimal minPrice,
            @RequestParam(value = "maxPrice", required = false) BigDecimal maxPrice,
            @RequestParam(value = "location", required = false) String location,
            @RequestParam(value = "isPro", required = false) Boolean isPro,
            @RequestParam(value = "languages", required = false) List<String> languages,
            @PageableDefault(size = 20, sort = "rating") Pageable pageable) {

        log.debug("REST request to get coaches with filters");

        // Build filter DTO
        CoachFilterDTO filter = new CoachFilterDTO();
        filter.setSearchQuery(searchQuery);
        filter.setTargetGoals(targetGoals);
        filter.setSpecialties(specialties);
        filter.setEnvironments(environments);
        filter.setSpecialConsiderations(specialConsiderations);
        filter.setMinExperience(minExperience);
        filter.setMaxExperience(maxExperience);
        filter.setMinRating(minRating);
        filter.setMaxRating(maxRating);
        filter.setMinPrice(minPrice);
        filter.setMaxPrice(maxPrice);
        filter.setLocation(location);
        filter.setIsPro(isPro);
        filter.setLanguages(languages);

        Page<CoachDTO> coaches = coachService.getAllCoaches(filter, pageable);
        return ResponseEntity.ok(coaches);
    }

    /**
     * GET /api/coaches/search : Search coaches by query
     */
    @GetMapping("/search")
    public ResponseEntity<Page<CoachDTO>> searchCoaches(
            @RequestParam("q") String searchQuery,
            @PageableDefault(size = 20, sort = "rating") Pageable pageable) {

        log.debug("REST request to search coaches with query: {}", searchQuery);

        Page<CoachDTO> coaches = coachService.searchCoaches(searchQuery, pageable);
        return ResponseEntity.ok(coaches);
    }

    /**
     * GET /api/coaches/top-rated : Get top rated coaches
     */
    @GetMapping("/top-rated")
    public ResponseEntity<Page<CoachDTO>> getTopRatedCoaches(
            @PageableDefault(size = 10, sort = "rating") Pageable pageable) {

        log.debug("REST request to get top rated coaches");

        Page<CoachDTO> coaches = coachService.getTopRatedCoaches(pageable);
        return ResponseEntity.ok(coaches);
    }

    /**
     * GET /api/coaches/pro : Get pro coaches
     */
    @GetMapping("/pro")
    public ResponseEntity<Page<CoachDTO>> getProCoaches(
            @PageableDefault(size = 20, sort = "rating") Pageable pageable) {

        log.debug("REST request to get pro coaches");

        Page<CoachDTO> coaches = coachService.getProCoaches(pageable);
        return ResponseEntity.ok(coaches);
    }

    /**
     * GET /api/coaches/experienced : Get most experienced coaches
     */
    @GetMapping("/experienced")
    public ResponseEntity<Page<CoachDTO>> getMostExperiencedCoaches(
            @PageableDefault(size = 20, sort = "experience") Pageable pageable) {

        log.debug("REST request to get most experienced coaches");

        Page<CoachDTO> coaches = coachService.getMostExperiencedCoaches(pageable);
        return ResponseEntity.ok(coaches);
    }

    /**
     * GET /api/coaches/filter-options : Get available filter options
     */
    @GetMapping("/filter-options")
    public ResponseEntity<CoachService.CoachFilterOptionsDTO> getFilterOptions() {
        log.debug("REST request to get coach filter options");

        CoachService.CoachFilterOptionsDTO options = coachService.getFilterOptions();
        return ResponseEntity.ok(options);
    }

    /**
     * GET /api/coaches/{id} : Get coach by ID with full details
     */
    @GetMapping("/{id}")
    public ResponseEntity<CoachDTO> getCoach(@PathVariable String id) {
        log.debug("REST request to get coach: {}", id);

        Optional<CoachDTO> coach = coachService.getCoachById(id);
        return coach.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    /**
     * GET /api/coaches/{id}/basic : Get coach by ID with basic info only
     */
    @GetMapping("/{id}/basic")
    public ResponseEntity<CoachDTO> getCoachBasic(@PathVariable String id) {
        log.debug("REST request to get coach basic info: {}", id);

        Optional<CoachDTO> coach = coachService.getCoachBasicById(id);
        return coach.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    /**
     * GET /api/coaches/{id}/stats : Get coach statistics
     */
    @GetMapping("/{id}/stats")
    public ResponseEntity<CoachService.CoachStatsDTO> getCoachStats(@PathVariable String id) {
        log.debug("REST request to get coach stats: {}", id);

        CoachService.CoachStatsDTO stats = coachService.getCoachStats(id);
        return ResponseEntity.ok(stats);
    }

    /**
     * GET /api/coaches/{id}/reviews : Get reviews for a coach
     */
    @GetMapping("/{id}/reviews")
    public ResponseEntity<Page<CoachReviewDTO>> getCoachReviews(
            @PathVariable String id,
            @PageableDefault(size = 10, sort = "reviewDate") Pageable pageable) {

        log.debug("REST request to get reviews for coach: {}", id);

        Page<CoachReviewDTO> reviews = coachService.getCoachReviews(id, pageable);
        return ResponseEntity.ok(reviews);
    }

    /**
     * GET /api/coaches/{id}/reviews/helpful : Get top helpful reviews for a coach
     */
    @GetMapping("/{id}/reviews/helpful")
    public ResponseEntity<Page<CoachReviewDTO>> getTopHelpfulReviews(
            @PathVariable String id,
            @PageableDefault(size = 5, sort = "helpfulCount") Pageable pageable) {

        log.debug("REST request to get top helpful reviews for coach: {}", id);

        Page<CoachReviewDTO> reviews = coachService.getTopHelpfulReviews(id, pageable);
        return ResponseEntity.ok(reviews);
    }

    /**
     * POST /api/coaches : Create a new coach
     */
    @PostMapping
    public ResponseEntity<CoachDTO> createCoach(@Valid @RequestBody CoachDTO coachDTO) {
        log.debug("REST request to create coach: {}", coachDTO.getName());

        CoachDTO result = coachService.createCoach(coachDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(result);
    }

    /**
     * PUT /api/coaches/{id} : Update an existing coach
     */
    @PutMapping("/{id}")
    public ResponseEntity<CoachDTO> updateCoach(
            @PathVariable String id,
            @Valid @RequestBody CoachDTO coachDTO) {

        log.debug("REST request to update coach: {}", id);

        Optional<CoachDTO> result = coachService.updateCoach(id, coachDTO);
        return result.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }

    /**
     * DELETE /api/coaches/{id} : Delete a coach
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCoach(@PathVariable String id) {
        log.debug("REST request to delete coach: {}", id);

        boolean deleted = coachService.deleteCoach(id);
        return deleted ? ResponseEntity.noContent().build() 
                      : ResponseEntity.notFound().build();
    }

    /**
     * POST /api/coaches/{id}/reviews : Add a review to a coach
     */
    @PostMapping("/{id}/reviews")
    public ResponseEntity<CoachReviewDTO> addReview(
            @PathVariable String id,
            @Valid @RequestBody CoachReviewDTO reviewDTO) {

        log.debug("REST request to add review for coach: {}", id);

        try {
            CoachReviewDTO result = coachService.addReview(id, reviewDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * PUT /api/coaches/reviews/{reviewId} : Update a review
     */
    @PutMapping("/reviews/{reviewId}")
    public ResponseEntity<CoachReviewDTO> updateReview(
            @PathVariable String reviewId,
            @Valid @RequestBody CoachReviewDTO reviewDTO) {

        log.debug("REST request to update review: {}", reviewId);

        Optional<CoachReviewDTO> result = coachService.updateReview(reviewId, reviewDTO);
        return result.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }

    /**
     * DELETE /api/coaches/reviews/{reviewId} : Delete a review
     */
    @DeleteMapping("/reviews/{reviewId}")
    public ResponseEntity<Void> deleteReview(@PathVariable String reviewId) {
        log.debug("REST request to delete review: {}", reviewId);

        boolean deleted = coachService.deleteReview(reviewId);
        return deleted ? ResponseEntity.noContent().build() 
                      : ResponseEntity.notFound().build();
    }

    /**
     * POST /api/coaches/reviews/{reviewId}/helpful : Mark review as helpful
     */
    @PostMapping("/reviews/{reviewId}/helpful")
    public ResponseEntity<Void> markReviewAsHelpful(@PathVariable String reviewId) {
        log.debug("REST request to mark review as helpful: {}", reviewId);

        boolean marked = coachService.markReviewAsHelpful(reviewId);
        return marked ? ResponseEntity.ok().build() 
                     : ResponseEntity.notFound().build();
    }
}
