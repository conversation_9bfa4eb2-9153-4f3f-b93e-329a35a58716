package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.MutableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Coach entity representing fitness coaches with their profiles, specialties, and certifications
 */
@Entity
@Table(name = "coach", indexes = {
    @Index(name = "idx_coach_name", columnList = "name"),
    @Index(name = "idx_coach_rating", columnList = "rating"),
    @Index(name = "idx_coach_location", columnList = "location"),
    @Index(name = "idx_coach_is_pro", columnList = "is_pro"),
    @Index(name = "idx_coach_price_per_session", columnList = "price_per_session")
})
public class Coach extends MutableEntity {

    @NotBlank
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "title", length = 150)
    private String title;

    @Column(name = "avatar", length = 500)
    private String avatar;

    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("10.0")
    @Column(name = "rating", nullable = false, precision = 3, scale = 2)
    private BigDecimal rating;

    @Column(name = "review_count", nullable = false)
    private Integer reviewCount = 0;

    @Column(name = "bio", columnDefinition = "TEXT")
    private String bio;

    @Column(name = "motto", length = 255)
    private String motto;

    @Column(name = "experience")
    private Integer experience;

    @Column(name = "is_pro", nullable = false)
    private Boolean isPro = false;

    @Column(name = "location", length = 100)
    private String location;

    @Column(name = "session_count")
    private Integer sessionCount = 0;

    @Column(name = "price_per_session", precision = 10, scale = 2)
    private BigDecimal pricePerSession;

    @Column(name = "video_url", length = 500)
    private String videoUrl;

    @Column(name = "languages", length = 255)
    private String languages; // Stored as comma-separated values

    @Column(name = "gallery", columnDefinition = "TEXT")
    private String gallery; // Stored as JSON array of URLs

    // Availability stored as JSON
    @Column(name = "availability", columnDefinition = "TEXT")
    private String availability;

    // Price range stored as JSON
    @Column(name = "price_range", columnDefinition = "TEXT")
    private String priceRange;

    // One-to-Many relationships
    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachReview> reviews = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachCertification> certifications = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachTargetGoal> targetGoals = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachSpecialty> specialties = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachEnvironment> environments = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachSpecialConsideration> specialConsiderations = new ArrayList<>();

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoachWorkout> workouts = new ArrayList<>();

    // Constructors
    public Coach() {}

    public Coach(String name, String avatar, BigDecimal rating) {
        this.name = name;
        this.avatar = avatar;
        this.rating = rating;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getMotto() {
        return motto;
    }

    public void setMotto(String motto) {
        this.motto = motto;
    }

    public Integer getExperience() {
        return experience;
    }

    public void setExperience(Integer experience) {
        this.experience = experience;
    }

    public Boolean getIsPro() {
        return isPro;
    }

    public void setIsPro(Boolean isPro) {
        this.isPro = isPro;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public BigDecimal getPricePerSession() {
        return pricePerSession;
    }

    public void setPricePerSession(BigDecimal pricePerSession) {
        this.pricePerSession = pricePerSession;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getGallery() {
        return gallery;
    }

    public void setGallery(String gallery) {
        this.gallery = gallery;
    }

    public String getAvailability() {
        return availability;
    }

    public void setAvailability(String availability) {
        this.availability = availability;
    }

    public String getPriceRange() {
        return priceRange;
    }

    public void setPriceRange(String priceRange) {
        this.priceRange = priceRange;
    }

    public List<CoachReview> getReviews() {
        return reviews;
    }

    public void setReviews(List<CoachReview> reviews) {
        this.reviews = reviews;
    }

    public List<CoachCertification> getCertifications() {
        return certifications;
    }

    public void setCertifications(List<CoachCertification> certifications) {
        this.certifications = certifications;
    }

    public List<CoachTargetGoal> getTargetGoals() {
        return targetGoals;
    }

    public void setTargetGoals(List<CoachTargetGoal> targetGoals) {
        this.targetGoals = targetGoals;
    }

    public List<CoachSpecialty> getSpecialties() {
        return specialties;
    }

    public void setSpecialties(List<CoachSpecialty> specialties) {
        this.specialties = specialties;
    }

    public List<CoachEnvironment> getEnvironments() {
        return environments;
    }

    public void setEnvironments(List<CoachEnvironment> environments) {
        this.environments = environments;
    }

    public List<CoachSpecialConsideration> getSpecialConsiderations() {
        return specialConsiderations;
    }

    public void setSpecialConsiderations(List<CoachSpecialConsideration> specialConsiderations) {
        this.specialConsiderations = specialConsiderations;
    }

    public List<CoachWorkout> getWorkouts() {
        return workouts;
    }

    public void setWorkouts(List<CoachWorkout> workouts) {
        this.workouts = workouts;
    }

    // Helper methods
    public void addReview(CoachReview review) {
        reviews.add(review);
        review.setCoach(this);
        this.reviewCount = reviews.size();
    }

    public void removeReview(CoachReview review) {
        reviews.remove(review);
        review.setCoach(null);
        this.reviewCount = reviews.size();
    }

    public void addCertification(CoachCertification certification) {
        certifications.add(certification);
        certification.setCoach(this);
    }

    public void removeCertification(CoachCertification certification) {
        certifications.remove(certification);
        certification.setCoach(null);
    }
}
