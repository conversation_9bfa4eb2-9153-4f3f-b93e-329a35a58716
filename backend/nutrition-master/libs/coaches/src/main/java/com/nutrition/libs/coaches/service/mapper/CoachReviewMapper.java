package com.nutrition.libs.coaches.service.mapper;

import com.nutrition.libs.coaches.domain.CoachReview;
import com.nutrition.libs.coaches.service.dto.CoachReviewDTO;
import org.mapstruct.*;

/**
 * MapStruct mapper for CoachReview entity and DTO conversion
 */
@Mapper(componentModel = "spring")
public abstract class CoachReviewMapper {

    /**
     * Convert CoachReview entity to DTO
     */
    @Mapping(source = "uuid", target = "id")
    @Mapping(source = "coach.uuid", target = "coachId")
    public abstract CoachReviewDTO toDTO(CoachReview review);

    /**
     * Convert DTO to CoachReview entity
     */
    @Mapping(source = "id", target = "uuid")
    @Mapping(target = "coach", ignore = true) // Set separately in service
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    public abstract CoachReview toEntity(CoachReviewDTO dto);

    /**
     * Update entity from DTO
     */
    @Mapping(target = "uuid", ignore = true)
    @Mapping(target = "coach", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    public abstract void updateEntityFromDTO(CoachReviewDTO dto, @MappingTarget CoachReview entity);
}
