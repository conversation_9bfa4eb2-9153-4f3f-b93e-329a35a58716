package com.nutrition.libs.coaches.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for Coach entity
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoachDTO {

    private String id;
    private String name;
    private String title;
    private String avatar;
    private BigDecimal rating;
    private Integer reviewCount;
    private String bio;
    private String motto;
    private Integer experience;
    private Boolean isPro;
    private String location;
    private Integer sessionCount;
    private BigDecimal pricePerSession;
    private String videoUrl;
    private List<String> languages;
    private List<String> gallery;
    private AvailabilityDTO availability;
    private PriceRangeDTO priceRange;

    // Related entities
    private List<CoachReviewDTO> reviews;
    private List<CertificationDTO> certifications;
    private List<String> targetGoals;
    private List<SpecialtyDTO> specialties;
    private List<EnvironmentDTO> environments;
    private List<String> specialConsiderations;
    private List<WorkoutDTO> workouts;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    // Constructors
    public CoachDTO() {}

    public CoachDTO(String id, String name, String avatar, BigDecimal rating) {
        this.id = id;
        this.name = name;
        this.avatar = avatar;
        this.rating = rating;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getMotto() {
        return motto;
    }

    public void setMotto(String motto) {
        this.motto = motto;
    }

    public Integer getExperience() {
        return experience;
    }

    public void setExperience(Integer experience) {
        this.experience = experience;
    }

    public Boolean getIsPro() {
        return isPro;
    }

    public void setIsPro(Boolean isPro) {
        this.isPro = isPro;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public BigDecimal getPricePerSession() {
        return pricePerSession;
    }

    public void setPricePerSession(BigDecimal pricePerSession) {
        this.pricePerSession = pricePerSession;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    public List<String> getGallery() {
        return gallery;
    }

    public void setGallery(List<String> gallery) {
        this.gallery = gallery;
    }

    public AvailabilityDTO getAvailability() {
        return availability;
    }

    public void setAvailability(AvailabilityDTO availability) {
        this.availability = availability;
    }

    public PriceRangeDTO getPriceRange() {
        return priceRange;
    }

    public void setPriceRange(PriceRangeDTO priceRange) {
        this.priceRange = priceRange;
    }

    public List<CoachReviewDTO> getReviews() {
        return reviews;
    }

    public void setReviews(List<CoachReviewDTO> reviews) {
        this.reviews = reviews;
    }

    public List<CertificationDTO> getCertifications() {
        return certifications;
    }

    public void setCertifications(List<CertificationDTO> certifications) {
        this.certifications = certifications;
    }

    public List<String> getTargetGoals() {
        return targetGoals;
    }

    public void setTargetGoals(List<String> targetGoals) {
        this.targetGoals = targetGoals;
    }

    public List<SpecialtyDTO> getSpecialties() {
        return specialties;
    }

    public void setSpecialties(List<SpecialtyDTO> specialties) {
        this.specialties = specialties;
    }

    public List<EnvironmentDTO> getEnvironments() {
        return environments;
    }

    public void setEnvironments(List<EnvironmentDTO> environments) {
        this.environments = environments;
    }

    public List<String> getSpecialConsiderations() {
        return specialConsiderations;
    }

    public void setSpecialConsiderations(List<String> specialConsiderations) {
        this.specialConsiderations = specialConsiderations;
    }

    public List<WorkoutDTO> getWorkouts() {
        return workouts;
    }

    public void setWorkouts(List<WorkoutDTO> workouts) {
        this.workouts = workouts;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Nested DTO for availability information
     */
    public static class AvailabilityDTO {
        private List<String> days;
        private String timeRange;

        public AvailabilityDTO() {}

        public AvailabilityDTO(List<String> days, String timeRange) {
            this.days = days;
            this.timeRange = timeRange;
        }

        public List<String> getDays() {
            return days;
        }

        public void setDays(List<String> days) {
            this.days = days;
        }

        public String getTimeRange() {
            return timeRange;
        }

        public void setTimeRange(String timeRange) {
            this.timeRange = timeRange;
        }
    }

    /**
     * Nested DTO for price range information
     */
    public static class PriceRangeDTO {
        private BigDecimal min;
        private BigDecimal max;
        private String currency;

        public PriceRangeDTO() {}

        public PriceRangeDTO(BigDecimal min, BigDecimal max, String currency) {
            this.min = min;
            this.max = max;
            this.currency = currency;
        }

        public BigDecimal getMin() {
            return min;
        }

        public void setMin(BigDecimal min) {
            this.min = min;
        }

        public BigDecimal getMax() {
            return max;
        }

        public void setMax(BigDecimal max) {
            this.max = max;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
