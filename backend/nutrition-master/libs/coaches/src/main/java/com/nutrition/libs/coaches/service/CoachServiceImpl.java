package com.nutrition.libs.coaches.service;

import com.nutrition.libs.coaches.domain.Coach;
import com.nutrition.libs.coaches.domain.CoachReview;
import com.nutrition.libs.coaches.repository.CoachRepository;
import com.nutrition.libs.coaches.repository.CoachReviewRepository;
import com.nutrition.libs.coaches.repository.CoachSpecification;
import com.nutrition.libs.coaches.service.dto.CoachDTO;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import com.nutrition.libs.coaches.service.dto.CoachReviewDTO;
import com.nutrition.libs.coaches.service.mapper.CoachMapper;
import com.nutrition.libs.coaches.service.mapper.CoachReviewMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service implementation for Coach operations with lazy loading and dynamic filtering
 */
@Service
@Transactional
public class CoachServiceImpl implements CoachService {

    private static final Logger log = LoggerFactory.getLogger(CoachServiceImpl.class);

    private final CoachRepository coachRepository;
    private final CoachReviewRepository coachReviewRepository;
    private final CoachMapper coachMapper;
    private final CoachReviewMapper coachReviewMapper;

    public CoachServiceImpl(CoachRepository coachRepository,
                           CoachReviewRepository coachReviewRepository,
                           CoachMapper coachMapper,
                           CoachReviewMapper coachReviewMapper) {
        this.coachRepository = coachRepository;
        this.coachReviewRepository = coachReviewRepository;
        this.coachMapper = coachMapper;
        this.coachReviewMapper = coachReviewMapper;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getAllCoaches(CoachFilterDTO filter, Pageable pageable) {
        log.debug("Getting all coaches with filter: {} and pageable: {}", filter, pageable);

        if (filter != null && filter.hasFilters()) {
            // Use dynamic filtering with JPA Specification
            Specification<Coach> spec = CoachSpecification.createSpecification(filter);
            Page<Coach> coaches = coachRepository.findAll(spec, pageable);
            return coaches.map(coachMapper::toBasicDTO);
        } else {
            // Use basic query without joins for better performance
            Page<Coach> coaches = coachRepository.findAllBasic(pageable);
            return coaches.map(coachMapper::toBasicDTO);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CoachDTO> getCoachById(String id) {
        log.debug("Getting coach by id: {}", id);

        Optional<Coach> coach = coachRepository.findByIdWithDetails(id);
        return coach.map(coachMapper::toFullDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CoachDTO> getCoachBasicById(String id) {
        log.debug("Getting coach basic info by id: {}", id);

        Optional<Coach> coach = coachRepository.findById(id);
        return coach.filter(c -> !c.isDeleted()).map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> searchCoaches(String searchQuery, Pageable pageable) {
        log.debug("Searching coaches with query: {}", searchQuery);

        Page<Coach> coaches = coachRepository.searchCoaches(searchQuery, pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getCoachesBySpecialty(String specialtyName, Pageable pageable) {
        log.debug("Getting coaches by specialty: {}", specialtyName);

        Page<Coach> coaches = coachRepository.findBySpecialtyName(specialtyName, pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getCoachesByTargetGoal(String goalName, Pageable pageable) {
        log.debug("Getting coaches by target goal: {}", goalName);

        Page<Coach> coaches = coachRepository.findByTargetGoalName(goalName, pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getCoachesByLocation(String location, Pageable pageable) {
        log.debug("Getting coaches by location: {}", location);

        Page<Coach> coaches = coachRepository.findByLocationContainingIgnoreCase(location, pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getTopRatedCoaches(Pageable pageable) {
        log.debug("Getting top rated coaches");

        Page<Coach> coaches = coachRepository.findTopRatedCoaches(pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getProCoaches(Pageable pageable) {
        log.debug("Getting pro coaches");

        Page<Coach> coaches = coachRepository.findProCoaches(pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachDTO> getMostExperiencedCoaches(Pageable pageable) {
        log.debug("Getting most experienced coaches");

        Page<Coach> coaches = coachRepository.findMostExperiencedCoaches(pageable);
        return coaches.map(coachMapper::toBasicDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachReviewDTO> getCoachReviews(String coachId, Pageable pageable) {
        log.debug("Getting reviews for coach: {}", coachId);

        Page<CoachReview> reviews = coachReviewRepository.findByCoachIdOrderByReviewDateDesc(coachId, pageable);
        return reviews.map(coachReviewMapper::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CoachReviewDTO> getTopHelpfulReviews(String coachId, Pageable pageable) {
        log.debug("Getting top helpful reviews for coach: {}", coachId);

        Page<CoachReview> reviews = coachReviewRepository.findTopHelpfulReviewsByCoachId(coachId, pageable);
        return reviews.map(coachReviewMapper::toDTO);
    }

    @Override
    public CoachDTO createCoach(CoachDTO coachDTO) {
        log.debug("Creating new coach: {}", coachDTO.getName());

        Coach coach = coachMapper.toEntity(coachDTO);
        Coach savedCoach = coachRepository.save(coach);
        return coachMapper.toBasicDTO(savedCoach);
    }

    @Override
    public Optional<CoachDTO> updateCoach(String id, CoachDTO coachDTO) {
        log.debug("Updating coach with id: {}", id);

        return coachRepository.findById(id)
            .filter(coach -> !coach.isDeleted())
            .map(existingCoach -> {
                coachMapper.updateEntityFromDTO(coachDTO, existingCoach);
                Coach updatedCoach = coachRepository.save(existingCoach);
                return coachMapper.toBasicDTO(updatedCoach);
            });
    }

    @Override
    public boolean deleteCoach(String id) {
        log.debug("Deleting coach with id: {}", id);

        return coachRepository.findById(id)
            .filter(coach -> !coach.isDeleted())
            .map(coach -> {
                coach.setDeleted(true);
                coachRepository.save(coach);
                return true;
            })
            .orElse(false);
    }

    @Override
    public CoachReviewDTO addReview(String coachId, CoachReviewDTO reviewDTO) {
        log.debug("Adding review for coach: {}", coachId);

        Optional<Coach> coachOpt = coachRepository.findById(coachId);
        if (coachOpt.isEmpty() || coachOpt.get().isDeleted()) {
            throw new IllegalArgumentException("Coach not found with id: " + coachId);
        }

        Coach coach = coachOpt.get();
        CoachReview review = coachReviewMapper.toEntity(reviewDTO);
        review.setCoach(coach);

        CoachReview savedReview = coachReviewRepository.save(review);

        // Update coach's review count and average rating
        updateCoachRatingStats(coach);

        return coachReviewMapper.toDTO(savedReview);
    }

    @Override
    public Optional<CoachReviewDTO> updateReview(String reviewId, CoachReviewDTO reviewDTO) {
        log.debug("Updating review with id: {}", reviewId);

        return coachReviewRepository.findById(reviewId)
            .filter(review -> !review.isDeleted())
            .map(existingReview -> {
                coachReviewMapper.updateEntityFromDTO(reviewDTO, existingReview);
                CoachReview updatedReview = coachReviewRepository.save(existingReview);

                // Update coach's rating stats
                updateCoachRatingStats(existingReview.getCoach());

                return coachReviewMapper.toDTO(updatedReview);
            });
    }

    @Override
    public boolean deleteReview(String reviewId) {
        log.debug("Deleting review with id: {}", reviewId);

        return coachReviewRepository.findById(reviewId)
            .filter(review -> !review.isDeleted())
            .map(review -> {
                review.setDeleted(true);
                coachReviewRepository.save(review);

                // Update coach's rating stats
                updateCoachRatingStats(review.getCoach());

                return true;
            })
            .orElse(false);
    }

    @Override
    public boolean markReviewAsHelpful(String reviewId) {
        log.debug("Marking review as helpful: {}", reviewId);

        return coachReviewRepository.findById(reviewId)
            .filter(review -> !review.isDeleted())
            .map(review -> {
                review.incrementHelpfulCount();
                coachReviewRepository.save(review);
                return true;
            })
            .orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public CoachFilterOptionsDTO getFilterOptions() {
        log.debug("Getting filter options");

        CoachFilterOptionsDTO options = new CoachFilterOptionsDTO();
        options.setSpecialties(coachRepository.findAllSpecialtyNames());
        options.setTargetGoals(coachRepository.findAllTargetGoalNames());
        options.setEnvironments(coachRepository.findAllEnvironmentNames());
        options.setSpecialConsiderations(coachRepository.findAllSpecialConsiderationNames());
        options.setLocations(coachRepository.findAllLocations());

        return options;
    }

    @Override
    @Transactional(readOnly = true)
    public CoachStatsDTO getCoachStats(String coachId) {
        log.debug("Getting stats for coach: {}", coachId);

        CoachStatsDTO stats = new CoachStatsDTO();

        // Get total reviews
        Long totalReviews = coachReviewRepository.countByCoachId(coachId);
        stats.setTotalReviews(totalReviews);

        // Get average rating
        BigDecimal avgRating = coachReviewRepository.calculateAverageRatingByCoachId(coachId);
        stats.setAverageRating(avgRating != null ? avgRating.doubleValue() : 0.0);

        // Get rating distribution
        List<Object[]> ratingDistData = coachReviewRepository.getRatingDistributionByCoachId(coachId);
        List<CoachStatsDTO.RatingDistribution> ratingDist = ratingDistData.stream()
            .map(data -> new CoachStatsDTO.RatingDistribution(
                ((BigDecimal) data[0]).doubleValue(),
                (Long) data[1]
            ))
            .collect(Collectors.toList());
        stats.setRatingDistribution(ratingDist);

        // Get total sessions from coach entity
        coachRepository.findById(coachId)
            .filter(coach -> !coach.isDeleted())
            .ifPresent(coach -> stats.setTotalSessions(coach.getSessionCount().longValue()));

        return stats;
    }

    /**
     * Update coach's rating statistics based on current reviews
     */
    private void updateCoachRatingStats(Coach coach) {
        Long reviewCount = coachReviewRepository.countByCoachId(coach.getUuid());
        BigDecimal avgRating = coachReviewRepository.calculateAverageRatingByCoachId(coach.getUuid());

        coach.setReviewCount(reviewCount.intValue());
        coach.setRating(avgRating != null ? avgRating : BigDecimal.ZERO);

        coachRepository.save(coach);
    }
}
