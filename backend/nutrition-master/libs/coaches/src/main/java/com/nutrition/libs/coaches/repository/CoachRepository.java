package com.nutrition.libs.coaches.repository;

import com.nutrition.libs.coaches.domain.Coach;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Coach entity operations with support for dynamic filtering and sorting
 */
@Repository
public interface CoachRepository extends JpaRepository<Coach, String>, JpaSpecificationExecutor<Coach> {

    /**
     * Find coach by ID with all related entities loaded
     */
    @Query("SELECT c FROM Coach c " +
           "LEFT JOIN FETCH c.reviews " +
           "LEFT JOIN FETCH c.certifications " +
           "LEFT JOIN FETCH c.targetGoals " +
           "LEFT JOIN FETCH c.specialties " +
           "LEFT JOIN FETCH c.environments " +
           "LEFT JOIN FETCH c.specialConsiderations " +
           "LEFT JOIN FETCH c.workouts " +
           "WHERE c.uuid = :id AND c.deleted = false")
    Optional<Coach> findByIdWithDetails(@Param("id") String id);

    /**
     * Find coaches by name containing search term (case insensitive)
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Coach> findByNameContainingIgnoreCase(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find coaches by location
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND " +
           "LOWER(c.location) LIKE LOWER(CONCAT('%', :location, '%'))")
    Page<Coach> findByLocationContainingIgnoreCase(@Param("location") String location, Pageable pageable);

    /**
     * Find coaches with rating greater than or equal to minimum rating
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND c.rating >= :minRating")
    Page<Coach> findByRatingGreaterThanEqual(@Param("minRating") BigDecimal minRating, Pageable pageable);

    /**
     * Find coaches by pro status
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND c.isPro = :isPro")
    Page<Coach> findByIsPro(@Param("isPro") Boolean isPro, Pageable pageable);

    /**
     * Find coaches with price per session within range
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND " +
           "c.pricePerSession >= :minPrice AND c.pricePerSession <= :maxPrice")
    Page<Coach> findByPricePerSessionBetween(@Param("minPrice") BigDecimal minPrice, 
                                           @Param("maxPrice") BigDecimal maxPrice, 
                                           Pageable pageable);

    /**
     * Find coaches by specialty name
     */
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.specialties s " +
           "WHERE c.deleted = false AND LOWER(s.name) = LOWER(:specialtyName)")
    Page<Coach> findBySpecialtyName(@Param("specialtyName") String specialtyName, Pageable pageable);

    /**
     * Find coaches by target goal name
     */
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.targetGoals tg " +
           "WHERE c.deleted = false AND LOWER(tg.name) = LOWER(:goalName)")
    Page<Coach> findByTargetGoalName(@Param("goalName") String goalName, Pageable pageable);

    /**
     * Find coaches by environment name
     */
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.environments e " +
           "WHERE c.deleted = false AND LOWER(e.name) = LOWER(:environmentName)")
    Page<Coach> findByEnvironmentName(@Param("environmentName") String environmentName, Pageable pageable);

    /**
     * Find coaches by special consideration name
     */
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.specialConsiderations sc " +
           "WHERE c.deleted = false AND LOWER(sc.name) = LOWER(:considerationName)")
    Page<Coach> findBySpecialConsiderationName(@Param("considerationName") String considerationName, Pageable pageable);

    /**
     * Find coaches with experience greater than or equal to minimum years
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND c.experience >= :minExperience")
    Page<Coach> findByExperienceGreaterThanEqual(@Param("minExperience") Integer minExperience, Pageable pageable);

    /**
     * Search coaches by multiple criteria (name, bio, title, location)
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.bio) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.location) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Coach> searchCoaches(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find top rated coaches
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false ORDER BY c.rating DESC, c.reviewCount DESC")
    Page<Coach> findTopRatedCoaches(Pageable pageable);

    /**
     * Find most experienced coaches
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND c.experience IS NOT NULL " +
           "ORDER BY c.experience DESC, c.rating DESC")
    Page<Coach> findMostExperiencedCoaches(Pageable pageable);

    /**
     * Find pro coaches
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false AND c.isPro = true " +
           "ORDER BY c.rating DESC, c.reviewCount DESC")
    Page<Coach> findProCoaches(Pageable pageable);

    /**
     * Count coaches by specialty
     */
    @Query("SELECT COUNT(DISTINCT c) FROM Coach c " +
           "JOIN c.specialties s " +
           "WHERE c.deleted = false AND LOWER(s.name) = LOWER(:specialtyName)")
    Long countBySpecialtyName(@Param("specialtyName") String specialtyName);

    /**
     * Get all unique specialties
     */
    @Query("SELECT DISTINCT s.name FROM CoachSpecialty s " +
           "JOIN s.coach c WHERE c.deleted = false " +
           "ORDER BY s.name")
    List<String> findAllSpecialtyNames();

    /**
     * Get all unique target goals
     */
    @Query("SELECT DISTINCT tg.name FROM CoachTargetGoal tg " +
           "JOIN tg.coach c WHERE c.deleted = false " +
           "ORDER BY tg.name")
    List<String> findAllTargetGoalNames();

    /**
     * Get all unique environments
     */
    @Query("SELECT DISTINCT e.name FROM CoachEnvironment e " +
           "JOIN e.coach c WHERE c.deleted = false " +
           "ORDER BY e.name")
    List<String> findAllEnvironmentNames();

    /**
     * Get all unique special considerations
     */
    @Query("SELECT DISTINCT sc.name FROM CoachSpecialConsideration sc " +
           "JOIN sc.coach c WHERE c.deleted = false " +
           "ORDER BY sc.name")
    List<String> findAllSpecialConsiderationNames();

    /**
     * Get all unique locations
     */
    @Query("SELECT DISTINCT c.location FROM Coach c " +
           "WHERE c.deleted = false AND c.location IS NOT NULL " +
           "ORDER BY c.location")
    List<String> findAllLocations();

    /**
     * Find all coaches with basic info (without lazy loaded collections)
     */
    @Query("SELECT c FROM Coach c WHERE c.deleted = false")
    Page<Coach> findAllBasic(Pageable pageable);
}
