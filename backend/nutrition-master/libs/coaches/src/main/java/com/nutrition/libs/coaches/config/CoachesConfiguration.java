package com.nutrition.libs.coaches.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Configuration class for Coaches module
 */
@Configuration
@ComponentScan(basePackages = "com.nutrition.libs.coaches")
@EntityScan(basePackages = "com.nutrition.libs.coaches.domain")
@EnableJpaRepositories(basePackages = "com.nutrition.libs.coaches.repository")
@EnableTransactionManagement
public class CoachesConfiguration {
    // Configuration class for auto-configuration of coaches module
}
