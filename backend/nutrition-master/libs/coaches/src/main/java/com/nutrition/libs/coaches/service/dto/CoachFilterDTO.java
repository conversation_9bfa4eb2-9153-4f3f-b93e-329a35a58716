package com.nutrition.libs.coaches.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.List;

/**
 * Data Transfer Object for Coach filtering criteria
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoachFilterDTO {

    private String searchQuery;
    private List<String> targetGoals;
    private List<String> specialties;
    private List<String> environments;
    private List<String> specialConsiderations;
    private Integer minExperience;
    private Integer maxExperience;
    private BigDecimal minRating;
    private BigDecimal maxRating;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private String location;
    private Boolean isPro;
    private List<String> languages;

    // Constructors
    public CoachFilterDTO() {}

    // Getters and Setters
    public String getSearchQuery() {
        return searchQuery;
    }

    public void setSearchQuery(String searchQuery) {
        this.searchQuery = searchQuery;
    }

    public List<String> getTargetGoals() {
        return targetGoals;
    }

    public void setTargetGoals(List<String> targetGoals) {
        this.targetGoals = targetGoals;
    }

    public List<String> getSpecialties() {
        return specialties;
    }

    public void setSpecialties(List<String> specialties) {
        this.specialties = specialties;
    }

    public List<String> getEnvironments() {
        return environments;
    }

    public void setEnvironments(List<String> environments) {
        this.environments = environments;
    }

    public List<String> getSpecialConsiderations() {
        return specialConsiderations;
    }

    public void setSpecialConsiderations(List<String> specialConsiderations) {
        this.specialConsiderations = specialConsiderations;
    }

    public Integer getMinExperience() {
        return minExperience;
    }

    public void setMinExperience(Integer minExperience) {
        this.minExperience = minExperience;
    }

    public Integer getMaxExperience() {
        return maxExperience;
    }

    public void setMaxExperience(Integer maxExperience) {
        this.maxExperience = maxExperience;
    }

    public BigDecimal getMinRating() {
        return minRating;
    }

    public void setMinRating(BigDecimal minRating) {
        this.minRating = minRating;
    }

    public BigDecimal getMaxRating() {
        return maxRating;
    }

    public void setMaxRating(BigDecimal maxRating) {
        this.maxRating = maxRating;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Boolean getIsPro() {
        return isPro;
    }

    public void setIsPro(Boolean isPro) {
        this.isPro = isPro;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    /**
     * Check if any filter criteria is set
     */
    public boolean hasFilters() {
        return (searchQuery != null && !searchQuery.trim().isEmpty()) ||
               (targetGoals != null && !targetGoals.isEmpty()) ||
               (specialties != null && !specialties.isEmpty()) ||
               (environments != null && !environments.isEmpty()) ||
               (specialConsiderations != null && !specialConsiderations.isEmpty()) ||
               minExperience != null ||
               maxExperience != null ||
               minRating != null ||
               maxRating != null ||
               minPrice != null ||
               maxPrice != null ||
               (location != null && !location.trim().isEmpty()) ||
               isPro != null ||
               (languages != null && !languages.isEmpty());
    }
}
