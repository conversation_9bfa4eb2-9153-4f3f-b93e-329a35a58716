package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * CoachEnvironment entity representing environments where coaches can provide training
 */
@Entity
@Table(name = "coach_environment", indexes = {
    @Index(name = "idx_coach_env_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_env_name", columnList = "name")
})
public class CoachEnvironment extends BaseEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "icon", length = 100)
    private String icon;

    // Constructors
    public CoachEnvironment() {}

    public CoachEnvironment(Coach coach, String name) {
        this.coach = coach;
        this.name = name;
    }

    public CoachEnvironment(Coach coach, String name, String icon) {
        this.coach = coach;
        this.name = name;
        this.icon = icon;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
