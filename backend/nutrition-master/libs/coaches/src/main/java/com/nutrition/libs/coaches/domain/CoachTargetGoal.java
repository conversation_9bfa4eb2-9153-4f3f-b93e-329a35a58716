package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * CoachTargetGoal entity representing target goals that coaches specialize in
 */
@Entity
@Table(name = "coach_target_goal", indexes = {
    @Index(name = "idx_coach_goal_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_goal_name", columnList = "name"),
    @Index(name = "idx_coach_goal_level", columnList = "level")
})
public class CoachTargetGoal extends BaseEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Min(1)
    @Max(5)
    @Column(name = "level")
    private Integer level; // 1-5 scale representing expertise level

    // Constructors
    public CoachTargetGoal() {}

    public CoachTargetGoal(Coach coach, String name, Integer level) {
        this.coach = coach;
        this.name = name;
        this.level = level;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
