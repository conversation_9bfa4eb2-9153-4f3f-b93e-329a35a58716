package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.MutableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * CoachReview entity representing reviews and ratings for coaches
 */
@Entity
@Table(name = "coach_review", indexes = {
    @Index(name = "idx_coach_review_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_review_user_id", columnList = "user_id"),
    @Index(name = "idx_coach_review_rating", columnList = "rating"),
    @Index(name = "idx_coach_review_date", columnList = "review_date")
})
public class CoachReview extends MutableEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "user_id", nullable = false, length = 32)
    private String userId;

    @NotBlank
    @Column(name = "user_name", nullable = false, length = 100)
    private String userName;

    @NotBlank
    @Column(name = "reviewer_name", nullable = false, length = 100)
    private String reviewerName;

    @Column(name = "user_avatar", length = 500)
    private String userAvatar;

    @NotNull
    @DecimalMin("1.0")
    @DecimalMax("10.0")
    @Column(name = "rating", nullable = false, precision = 3, scale = 2)
    private BigDecimal rating;

    @NotBlank
    @Column(name = "comment", nullable = false, columnDefinition = "TEXT")
    private String comment;

    @NotNull
    @Column(name = "review_date", nullable = false)
    private LocalDateTime reviewDate;

    @Column(name = "helpful_count")
    private Integer helpfulCount = 0;

    @Column(name = "is_editable")
    private Boolean isEditable = false;

    // Constructors
    public CoachReview() {
        this.reviewDate = LocalDateTime.now();
    }

    public CoachReview(Coach coach, String userId, String userName, String reviewerName, 
                      BigDecimal rating, String comment) {
        this();
        this.coach = coach;
        this.userId = userId;
        this.userName = userName;
        this.reviewerName = reviewerName;
        this.rating = rating;
        this.comment = comment;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public LocalDateTime getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(LocalDateTime reviewDate) {
        this.reviewDate = reviewDate;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public Boolean getIsEditable() {
        return isEditable;
    }

    public void setIsEditable(Boolean isEditable) {
        this.isEditable = isEditable;
    }

    // Helper methods
    public void incrementHelpfulCount() {
        this.helpfulCount = (this.helpfulCount == null) ? 1 : this.helpfulCount + 1;
    }

    public void decrementHelpfulCount() {
        this.helpfulCount = (this.helpfulCount == null || this.helpfulCount <= 0) ? 0 : this.helpfulCount - 1;
    }

    @PrePersist
    protected void onCreate() {
        if (reviewDate == null) {
            reviewDate = LocalDateTime.now();
        }
        if (helpfulCount == null) {
            helpfulCount = 0;
        }
        if (isEditable == null) {
            isEditable = false;
        }
    }
}
