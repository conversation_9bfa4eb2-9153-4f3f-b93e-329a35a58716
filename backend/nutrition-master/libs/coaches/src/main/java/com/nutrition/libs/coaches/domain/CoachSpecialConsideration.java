package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * CoachSpecialConsideration entity representing special considerations that coaches can handle
 */
@Entity
@Table(name = "coach_special_consideration", indexes = {
    @Index(name = "idx_coach_spec_cons_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_spec_cons_name", columnList = "name")
})
public class CoachSpecialConsideration extends BaseEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    // Constructors
    public CoachSpecialConsideration() {}

    public CoachSpecialConsideration(Coach coach, String name) {
        this.coach = coach;
        this.name = name;
    }

    public CoachSpecialConsideration(Coach coach, String name, String description) {
        this.coach = coach;
        this.name = name;
        this.description = description;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
