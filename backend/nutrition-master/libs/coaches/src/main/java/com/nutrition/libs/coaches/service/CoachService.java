package com.nutrition.libs.coaches.service;

import com.nutrition.libs.coaches.service.dto.CoachDTO;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import com.nutrition.libs.coaches.service.dto.CoachReviewDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Coach operations
 */
public interface CoachService {

    /**
     * Get all coaches with pagination and optional filtering
     */
    Page<CoachDTO> getAllCoaches(CoachFilterDTO filter, Pageable pageable);

    /**
     * Get coach by ID with full details
     */
    Optional<CoachDTO> getCoachById(String id);

    /**
     * Get coach by ID with basic information only (no lazy loaded collections)
     */
    Optional<CoachDTO> getCoachBasicById(String id);

    /**
     * Search coaches by query string
     */
    Page<CoachDTO> searchCoaches(String searchQuery, Pageable pageable);

    /**
     * Get coaches by specialty
     */
    Page<CoachDTO> getCoachesBySpecialty(String specialtyName, Pageable pageable);

    /**
     * Get coaches by target goal
     */
    Page<CoachDTO> getCoachesByTargetGoal(String goalName, Pageable pageable);

    /**
     * Get coaches by location
     */
    Page<CoachDTO> getCoachesByLocation(String location, Pageable pageable);

    /**
     * Get top rated coaches
     */
    Page<CoachDTO> getTopRatedCoaches(Pageable pageable);

    /**
     * Get pro coaches
     */
    Page<CoachDTO> getProCoaches(Pageable pageable);

    /**
     * Get most experienced coaches
     */
    Page<CoachDTO> getMostExperiencedCoaches(Pageable pageable);

    /**
     * Get all reviews for a specific coach
     */
    Page<CoachReviewDTO> getCoachReviews(String coachId, Pageable pageable);

    /**
     * Get top helpful reviews for a coach
     */
    Page<CoachReviewDTO> getTopHelpfulReviews(String coachId, Pageable pageable);

    /**
     * Create a new coach
     */
    CoachDTO createCoach(CoachDTO coachDTO);

    /**
     * Update an existing coach
     */
    Optional<CoachDTO> updateCoach(String id, CoachDTO coachDTO);

    /**
     * Delete a coach (soft delete)
     */
    boolean deleteCoach(String id);

    /**
     * Add a review to a coach
     */
    CoachReviewDTO addReview(String coachId, CoachReviewDTO reviewDTO);

    /**
     * Update a review
     */
    Optional<CoachReviewDTO> updateReview(String reviewId, CoachReviewDTO reviewDTO);

    /**
     * Delete a review (soft delete)
     */
    boolean deleteReview(String reviewId);

    /**
     * Mark a review as helpful
     */
    boolean markReviewAsHelpful(String reviewId);

    /**
     * Get filter options for coaches
     */
    CoachFilterOptionsDTO getFilterOptions();

    /**
     * Get coach statistics
     */
    CoachStatsDTO getCoachStats(String coachId);

    /**
     * DTO for filter options
     */
    class CoachFilterOptionsDTO {
        private List<String> specialties;
        private List<String> targetGoals;
        private List<String> environments;
        private List<String> specialConsiderations;
        private List<String> locations;
        private List<String> languages;

        public CoachFilterOptionsDTO() {}

        public List<String> getSpecialties() {
            return specialties;
        }

        public void setSpecialties(List<String> specialties) {
            this.specialties = specialties;
        }

        public List<String> getTargetGoals() {
            return targetGoals;
        }

        public void setTargetGoals(List<String> targetGoals) {
            this.targetGoals = targetGoals;
        }

        public List<String> getEnvironments() {
            return environments;
        }

        public void setEnvironments(List<String> environments) {
            this.environments = environments;
        }

        public List<String> getSpecialConsiderations() {
            return specialConsiderations;
        }

        public void setSpecialConsiderations(List<String> specialConsiderations) {
            this.specialConsiderations = specialConsiderations;
        }

        public List<String> getLocations() {
            return locations;
        }

        public void setLocations(List<String> locations) {
            this.locations = locations;
        }

        public List<String> getLanguages() {
            return languages;
        }

        public void setLanguages(List<String> languages) {
            this.languages = languages;
        }
    }

    /**
     * DTO for coach statistics
     */
    class CoachStatsDTO {
        private Long totalReviews;
        private Double averageRating;
        private Long totalSessions;
        private List<RatingDistribution> ratingDistribution;

        public CoachStatsDTO() {}

        public Long getTotalReviews() {
            return totalReviews;
        }

        public void setTotalReviews(Long totalReviews) {
            this.totalReviews = totalReviews;
        }

        public Double getAverageRating() {
            return averageRating;
        }

        public void setAverageRating(Double averageRating) {
            this.averageRating = averageRating;
        }

        public Long getTotalSessions() {
            return totalSessions;
        }

        public void setTotalSessions(Long totalSessions) {
            this.totalSessions = totalSessions;
        }

        public List<RatingDistribution> getRatingDistribution() {
            return ratingDistribution;
        }

        public void setRatingDistribution(List<RatingDistribution> ratingDistribution) {
            this.ratingDistribution = ratingDistribution;
        }

        public static class RatingDistribution {
            private Double rating;
            private Long count;

            public RatingDistribution() {}

            public RatingDistribution(Double rating, Long count) {
                this.rating = rating;
                this.count = count;
            }

            public Double getRating() {
                return rating;
            }

            public void setRating(Double rating) {
                this.rating = rating;
            }

            public Long getCount() {
                return count;
            }

            public void setCount(Long count) {
                this.count = count;
            }
        }
    }
}
