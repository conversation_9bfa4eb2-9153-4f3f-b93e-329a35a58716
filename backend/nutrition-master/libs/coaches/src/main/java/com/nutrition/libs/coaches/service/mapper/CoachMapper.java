package com.nutrition.libs.coaches.service.mapper;

import com.nutrition.libs.coaches.domain.*;
import com.nutrition.libs.coaches.service.dto.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MapStruct mapper for Coach entity and DTO conversion
 */
@Mapper(componentModel = "spring", uses = {CoachReviewMapper.class})
public abstract class CoachMapper {

    private static final Logger log = LoggerFactory.getLogger(CoachMapper.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Convert Coach entity to full DTO with all related entities
     */
    @Mapping(source = "uuid", target = "id")
    @Mapping(source = "languages", target = "languages", qualifiedByName = "stringToList")
    @Mapping(source = "gallery", target = "gallery", qualifiedByName = "jsonToStringList")
    @Mapping(source = "availability", target = "availability", qualifiedByName = "jsonToAvailability")
    @Mapping(source = "priceRange", target = "priceRange", qualifiedByName = "jsonToPriceRange")
    @Mapping(source = "targetGoals", target = "targetGoals", qualifiedByName = "targetGoalsToStringList")
    @Mapping(source = "specialties", target = "specialties")
    @Mapping(source = "environments", target = "environments")
    @Mapping(source = "specialConsiderations", target = "specialConsiderations", qualifiedByName = "specialConsiderationsToStringList")
    @Mapping(source = "reviews", target = "reviews")
    @Mapping(source = "certifications", target = "certifications")
    @Mapping(source = "workouts", target = "workouts")
    public abstract CoachDTO toFullDTO(Coach coach);

    /**
     * Convert Coach entity to basic DTO without lazy loaded collections
     */
    @Mapping(source = "uuid", target = "id")
    @Mapping(source = "languages", target = "languages", qualifiedByName = "stringToList")
    @Mapping(source = "gallery", target = "gallery", qualifiedByName = "jsonToStringList")
    @Mapping(source = "availability", target = "availability", qualifiedByName = "jsonToAvailability")
    @Mapping(source = "priceRange", target = "priceRange", qualifiedByName = "jsonToPriceRange")
    @Mapping(target = "targetGoals", ignore = true)
    @Mapping(target = "specialties", ignore = true)
    @Mapping(target = "environments", ignore = true)
    @Mapping(target = "specialConsiderations", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "certifications", ignore = true)
    @Mapping(target = "workouts", ignore = true)
    public abstract CoachDTO toBasicDTO(Coach coach);

    /**
     * Convert DTO to entity
     */
    @Mapping(source = "id", target = "uuid")
    @Mapping(source = "languages", target = "languages", qualifiedByName = "listToString")
    @Mapping(source = "gallery", target = "gallery", qualifiedByName = "stringListToJson")
    @Mapping(source = "availability", target = "availability", qualifiedByName = "availabilityToJson")
    @Mapping(source = "priceRange", target = "priceRange", qualifiedByName = "priceRangeToJson")
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "certifications", ignore = true)
    @Mapping(target = "targetGoals", ignore = true)
    @Mapping(target = "specialties", ignore = true)
    @Mapping(target = "environments", ignore = true)
    @Mapping(target = "specialConsiderations", ignore = true)
    @Mapping(target = "workouts", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    public abstract Coach toEntity(CoachDTO dto);

    /**
     * Update entity from DTO
     */
    @Mapping(source = "languages", target = "languages", qualifiedByName = "listToString")
    @Mapping(source = "gallery", target = "gallery", qualifiedByName = "stringListToJson")
    @Mapping(source = "availability", target = "availability", qualifiedByName = "availabilityToJson")
    @Mapping(source = "priceRange", target = "priceRange", qualifiedByName = "priceRangeToJson")
    @Mapping(target = "uuid", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "certifications", ignore = true)
    @Mapping(target = "targetGoals", ignore = true)
    @Mapping(target = "specialties", ignore = true)
    @Mapping(target = "environments", ignore = true)
    @Mapping(target = "specialConsiderations", ignore = true)
    @Mapping(target = "workouts", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    public abstract void updateEntityFromDTO(CoachDTO dto, @MappingTarget Coach entity);

    // Specialty mapping
    @Mapping(source = "uuid", target = "id")
    public abstract SpecialtyDTO toSpecialtyDTO(CoachSpecialty specialty);

    // Environment mapping
    @Mapping(source = "uuid", target = "id")
    public abstract EnvironmentDTO toEnvironmentDTO(CoachEnvironment environment);

    // Certification mapping
    @Mapping(source = "uuid", target = "id")
    public abstract CertificationDTO toCertificationDTO(CoachCertification certification);

    // Workout mapping
    @Mapping(source = "uuid", target = "id")
    @Mapping(source = "coach.uuid", target = "coachId")
    @Mapping(source = "difficulty", target = "difficulty", qualifiedByName = "workoutDifficultyToString")
    public abstract WorkoutDTO toWorkoutDTO(CoachWorkout workout);

    // Custom mapping methods
    @Named("stringToList")
    protected List<String> stringToList(String value) {
        if (value == null || value.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(value.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    @Named("listToString")
    protected String listToString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return String.join(",", list);
    }

    @Named("jsonToStringList")
    protected List<String> jsonToStringList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON string list: {}", json, e);
            return Collections.emptyList();
        }
    }

    @Named("stringListToJson")
    protected String stringListToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize string list to JSON: {}", list, e);
            return null;
        }
    }

    @Named("jsonToAvailability")
    protected CoachDTO.AvailabilityDTO jsonToAvailability(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, CoachDTO.AvailabilityDTO.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse availability JSON: {}", json, e);
            return null;
        }
    }

    @Named("availabilityToJson")
    protected String availabilityToJson(CoachDTO.AvailabilityDTO availability) {
        if (availability == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(availability);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize availability to JSON: {}", availability, e);
            return null;
        }
    }

    @Named("jsonToPriceRange")
    protected CoachDTO.PriceRangeDTO jsonToPriceRange(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, CoachDTO.PriceRangeDTO.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse price range JSON: {}", json, e);
            return null;
        }
    }

    @Named("priceRangeToJson")
    protected String priceRangeToJson(CoachDTO.PriceRangeDTO priceRange) {
        if (priceRange == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(priceRange);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize price range to JSON: {}", priceRange, e);
            return null;
        }
    }

    @Named("targetGoalsToStringList")
    protected List<String> targetGoalsToStringList(List<CoachTargetGoal> targetGoals) {
        if (targetGoals == null) {
            return Collections.emptyList();
        }
        return targetGoals.stream()
                .map(CoachTargetGoal::getName)
                .collect(Collectors.toList());
    }

    @Named("specialConsiderationsToStringList")
    protected List<String> specialConsiderationsToStringList(List<CoachSpecialConsideration> considerations) {
        if (considerations == null) {
            return Collections.emptyList();
        }
        return considerations.stream()
                .map(CoachSpecialConsideration::getName)
                .collect(Collectors.toList());
    }

    @Named("workoutDifficultyToString")
    protected String workoutDifficultyToString(CoachWorkout.WorkoutDifficulty difficulty) {
        return difficulty != null ? difficulty.getDisplayName() : null;
    }
}
