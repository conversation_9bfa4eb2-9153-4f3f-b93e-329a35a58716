package com.nutrition.libs.coaches.domain;

import com.nutrition.libs.shared.domain.model.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * CoachCertification entity representing certifications held by coaches
 */
@Entity
@Table(name = "coach_certification", indexes = {
    @Index(name = "idx_coach_cert_coach_id", columnList = "coach_id"),
    @Index(name = "idx_coach_cert_name", columnList = "name"),
    @Index(name = "idx_coach_cert_verified", columnList = "verified")
})
public class CoachCertification extends BaseEntity {

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coach_id", nullable = false)
    private Coach coach;

    @NotBlank
    @Column(name = "name", nullable = false, length = 150)
    private String name;

    @Column(name = "issuer", length = 150)
    private String issuer;

    @NotBlank
    @Column(name = "organization", nullable = false, length = 150)
    private String organization;

    @Column(name = "year")
    private Integer year;

    @Column(name = "verified")
    private Boolean verified = false;

    // Constructors
    public CoachCertification() {}

    public CoachCertification(Coach coach, String name, String organization) {
        this.coach = coach;
        this.name = name;
        this.organization = organization;
    }

    // Getters and Setters
    public Coach getCoach() {
        return coach;
    }

    public void setCoach(Coach coach) {
        this.coach = coach;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(Boolean verified) {
        this.verified = verified;
    }

    @PrePersist
    protected void onCreate() {
        if (verified == null) {
            verified = false;
        }
    }
}
