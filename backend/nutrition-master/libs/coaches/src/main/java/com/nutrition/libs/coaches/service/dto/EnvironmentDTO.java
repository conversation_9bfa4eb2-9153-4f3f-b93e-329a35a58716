package com.nutrition.libs.coaches.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Data Transfer Object for CoachEnvironment entity
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnvironmentDTO {

    private String id;
    private String name;
    private String icon;

    // Constructors
    public EnvironmentDTO() {}

    public EnvironmentDTO(String name) {
        this.name = name;
    }

    public EnvironmentDTO(String name, String icon) {
        this.name = name;
        this.icon = icon;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
