package com.nutrition.libs.coaches.repository;

import com.nutrition.libs.coaches.domain.CoachReview;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Repository interface for CoachReview entity operations
 */
@Repository
public interface CoachReviewRepository extends JpaRepository<CoachReview, String> {

    /**
     * Find all reviews for a specific coach, ordered by date descending
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false " +
           "ORDER BY r.reviewDate DESC")
    Page<CoachReview> findByCoachIdOrderByReviewDateDesc(@Param("coachId") String coachId, Pageable pageable);

    /**
     * Find all reviews by a specific user
     */
    @Query("SELECT r FROM CoachReview r WHERE r.userId = :userId AND r.deleted = false " +
           "ORDER BY r.reviewDate DESC")
    Page<CoachReview> findByUserIdOrderByReviewDateDesc(@Param("userId") String userId, Pageable pageable);

    /**
     * Find reviews for a coach with rating greater than or equal to minimum rating
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.rating >= :minRating " +
           "AND r.deleted = false ORDER BY r.reviewDate DESC")
    Page<CoachReview> findByCoachIdAndRatingGreaterThanEqual(@Param("coachId") String coachId, 
                                                           @Param("minRating") BigDecimal minRating, 
                                                           Pageable pageable);

    /**
     * Count total reviews for a coach
     */
    @Query("SELECT COUNT(r) FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false")
    Long countByCoachId(@Param("coachId") String coachId);

    /**
     * Calculate average rating for a coach
     */
    @Query("SELECT AVG(r.rating) FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false")
    BigDecimal calculateAverageRatingByCoachId(@Param("coachId") String coachId);

    /**
     * Find top helpful reviews for a coach
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false " +
           "ORDER BY r.helpfulCount DESC, r.reviewDate DESC")
    Page<CoachReview> findTopHelpfulReviewsByCoachId(@Param("coachId") String coachId, Pageable pageable);

    /**
     * Find recent reviews for a coach (last 30 days)
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false " +
           "AND r.reviewDate >= CURRENT_DATE - 30 ORDER BY r.reviewDate DESC")
    List<CoachReview> findRecentReviewsByCoachId(@Param("coachId") String coachId);

    /**
     * Check if user has already reviewed a coach
     */
    @Query("SELECT COUNT(r) > 0 FROM CoachReview r WHERE r.coach.uuid = :coachId " +
           "AND r.userId = :userId AND r.deleted = false")
    boolean existsByCoachIdAndUserId(@Param("coachId") String coachId, @Param("userId") String userId);

    /**
     * Find user's review for a specific coach
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.userId = :userId " +
           "AND r.deleted = false")
    CoachReview findByCoachIdAndUserId(@Param("coachId") String coachId, @Param("userId") String userId);

    /**
     * Get rating distribution for a coach
     */
    @Query("SELECT r.rating, COUNT(r) FROM CoachReview r WHERE r.coach.uuid = :coachId " +
           "AND r.deleted = false GROUP BY r.rating ORDER BY r.rating DESC")
    List<Object[]> getRatingDistributionByCoachId(@Param("coachId") String coachId);

    /**
     * Find reviews with helpful count greater than threshold
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.helpfulCount >= :threshold " +
           "AND r.deleted = false ORDER BY r.helpfulCount DESC, r.reviewDate DESC")
    Page<CoachReview> findHelpfulReviewsByCoachId(@Param("coachId") String coachId, 
                                                 @Param("threshold") Integer threshold, 
                                                 Pageable pageable);

    /**
     * Search reviews by comment content
     */
    @Query("SELECT r FROM CoachReview r WHERE r.coach.uuid = :coachId AND r.deleted = false " +
           "AND LOWER(r.comment) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "ORDER BY r.reviewDate DESC")
    Page<CoachReview> searchReviewsByCoachIdAndComment(@Param("coachId") String coachId, 
                                                      @Param("searchTerm") String searchTerm, 
                                                      Pageable pageable);
}
