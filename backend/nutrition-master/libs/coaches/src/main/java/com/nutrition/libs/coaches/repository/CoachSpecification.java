package com.nutrition.libs.coaches.repository;

import com.nutrition.libs.coaches.domain.*;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification class for dynamic filtering of Coach entities
 */
public class CoachSpecification {

    /**
     * Create specification based on filter criteria
     */
    public static Specification<Coach> createSpecification(CoachFilterDTO filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Always exclude deleted coaches
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            // Search query - search in name, bio, title, location
            if (StringUtils.hasText(filter.getSearchQuery())) {
                String searchTerm = "%" + filter.getSearchQuery().toLowerCase() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")), searchTerm);
                Predicate bioPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("bio")), searchTerm);
                Predicate titlePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("title")), searchTerm);
                Predicate locationPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("location")), searchTerm);
                
                predicates.add(criteriaBuilder.or(namePredicate, bioPredicate, titlePredicate, locationPredicate));
            }

            // Target goals filter
            if (filter.getTargetGoals() != null && !filter.getTargetGoals().isEmpty()) {
                Join<Coach, CoachTargetGoal> targetGoalJoin = root.join("targetGoals", JoinType.INNER);
                predicates.add(targetGoalJoin.get("name").in(filter.getTargetGoals()));
            }

            // Specialties filter
            if (filter.getSpecialties() != null && !filter.getSpecialties().isEmpty()) {
                Join<Coach, CoachSpecialty> specialtyJoin = root.join("specialties", JoinType.INNER);
                predicates.add(specialtyJoin.get("name").in(filter.getSpecialties()));
            }

            // Environments filter
            if (filter.getEnvironments() != null && !filter.getEnvironments().isEmpty()) {
                Join<Coach, CoachEnvironment> environmentJoin = root.join("environments", JoinType.INNER);
                predicates.add(environmentJoin.get("name").in(filter.getEnvironments()));
            }

            // Special considerations filter
            if (filter.getSpecialConsiderations() != null && !filter.getSpecialConsiderations().isEmpty()) {
                Join<Coach, CoachSpecialConsideration> specialConsiderationJoin = 
                    root.join("specialConsiderations", JoinType.INNER);
                predicates.add(specialConsiderationJoin.get("name").in(filter.getSpecialConsiderations()));
            }

            // Experience level filter
            if (filter.getMinExperience() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                    root.get("experience"), filter.getMinExperience()));
            }
            if (filter.getMaxExperience() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                    root.get("experience"), filter.getMaxExperience()));
            }

            // Rating filter
            if (filter.getMinRating() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                    root.get("rating"), filter.getMinRating()));
            }
            if (filter.getMaxRating() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                    root.get("rating"), filter.getMaxRating()));
            }

            // Price range filter
            if (filter.getMinPrice() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                    root.get("pricePerSession"), filter.getMinPrice()));
            }
            if (filter.getMaxPrice() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                    root.get("pricePerSession"), filter.getMaxPrice()));
            }

            // Location filter
            if (StringUtils.hasText(filter.getLocation())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("location")), 
                    "%" + filter.getLocation().toLowerCase() + "%"));
            }

            // Pro status filter
            if (filter.getIsPro() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isPro"), filter.getIsPro()));
            }

            // Languages filter (stored as comma-separated string)
            if (filter.getLanguages() != null && !filter.getLanguages().isEmpty()) {
                List<Predicate> languagePredicates = new ArrayList<>();
                for (String language : filter.getLanguages()) {
                    languagePredicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("languages")), 
                        "%" + language.toLowerCase() + "%"));
                }
                predicates.add(criteriaBuilder.or(languagePredicates.toArray(new Predicate[0])));
            }

            // Ensure distinct results when joining with collections
            if (hasCollectionJoins(filter)) {
                query.distinct(true);
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Check if the filter requires collection joins that might cause duplicates
     */
    private static boolean hasCollectionJoins(CoachFilterDTO filter) {
        return (filter.getTargetGoals() != null && !filter.getTargetGoals().isEmpty()) ||
               (filter.getSpecialties() != null && !filter.getSpecialties().isEmpty()) ||
               (filter.getEnvironments() != null && !filter.getEnvironments().isEmpty()) ||
               (filter.getSpecialConsiderations() != null && !filter.getSpecialConsiderations().isEmpty());
    }

    /**
     * Specification for finding coaches by name
     */
    public static Specification<Coach> hasNameContaining(String name) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(name)) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.like(
                criteriaBuilder.lower(root.get("name")), 
                "%" + name.toLowerCase() + "%");
        };
    }

    /**
     * Specification for finding coaches by minimum rating
     */
    public static Specification<Coach> hasRatingGreaterThanOrEqual(BigDecimal minRating) {
        return (root, query, criteriaBuilder) -> {
            if (minRating == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.greaterThanOrEqualTo(root.get("rating"), minRating);
        };
    }

    /**
     * Specification for finding coaches by pro status
     */
    public static Specification<Coach> isProCoach(Boolean isPro) {
        return (root, query, criteriaBuilder) -> {
            if (isPro == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("isPro"), isPro);
        };
    }

    /**
     * Specification for finding coaches by location
     */
    public static Specification<Coach> hasLocationContaining(String location) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(location)) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.like(
                criteriaBuilder.lower(root.get("location")), 
                "%" + location.toLowerCase() + "%");
        };
    }

    /**
     * Specification for finding coaches by specialty
     */
    public static Specification<Coach> hasSpecialty(String specialtyName) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(specialtyName)) {
                return criteriaBuilder.conjunction();
            }
            Join<Coach, CoachSpecialty> specialtyJoin = root.join("specialties", JoinType.INNER);
            query.distinct(true);
            return criteriaBuilder.equal(
                criteriaBuilder.lower(specialtyJoin.get("name")), 
                specialtyName.toLowerCase());
        };
    }

    /**
     * Specification for finding coaches by target goal
     */
    public static Specification<Coach> hasTargetGoal(String goalName) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(goalName)) {
                return criteriaBuilder.conjunction();
            }
            Join<Coach, CoachTargetGoal> targetGoalJoin = root.join("targetGoals", JoinType.INNER);
            query.distinct(true);
            return criteriaBuilder.equal(
                criteriaBuilder.lower(targetGoalJoin.get("name")), 
                goalName.toLowerCase());
        };
    }

    /**
     * Specification for excluding deleted coaches
     */
    public static Specification<Coach> isNotDeleted() {
        return (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), false);
    }
}
