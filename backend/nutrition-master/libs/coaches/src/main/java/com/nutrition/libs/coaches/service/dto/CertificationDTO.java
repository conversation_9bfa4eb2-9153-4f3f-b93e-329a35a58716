package com.nutrition.libs.coaches.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Data Transfer Object for CoachCertification entity
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificationDTO {

    private String id;
    private String name;
    private String issuer;
    private String organization;
    private Integer year;
    private Boolean verified;

    // Constructors
    public CertificationDTO() {}

    public CertificationDTO(String name, String organization) {
        this.name = name;
        this.organization = organization;
    }

    public CertificationDTO(String name, String issuer, String organization, Integer year) {
        this.name = name;
        this.issuer = issuer;
        this.organization = organization;
        this.year = year;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(Boolean verified) {
        this.verified = verified;
    }
}
