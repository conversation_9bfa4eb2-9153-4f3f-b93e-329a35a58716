package com.nutrition.libs.coaches.service;

import com.nutrition.libs.coaches.domain.Coach;
import com.nutrition.libs.coaches.repository.CoachRepository;
import com.nutrition.libs.coaches.repository.CoachReviewRepository;
import com.nutrition.libs.coaches.service.dto.CoachDTO;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import com.nutrition.libs.coaches.service.mapper.CoachMapper;
import com.nutrition.libs.coaches.service.mapper.CoachReviewMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CoachService
 */
@ExtendWith(MockitoExtension.class)
class CoachServiceTest {

    @Mock
    private CoachRepository coachRepository;

    @Mock
    private CoachReviewRepository coachReviewRepository;

    @Mock
    private CoachMapper coachMapper;

    @Mock
    private CoachReviewMapper coachReviewMapper;

    @InjectMocks
    private CoachServiceImpl coachService;

    private Coach testCoach;
    private CoachDTO testCoachDTO;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        testCoach = new Coach();
        testCoach.setUuid("test-coach-id");
        testCoach.setName("Test Coach");
        testCoach.setRating(BigDecimal.valueOf(4.5));
        testCoach.setDeleted(false);

        testCoachDTO = new CoachDTO();
        testCoachDTO.setId("test-coach-id");
        testCoachDTO.setName("Test Coach");
        testCoachDTO.setRating(BigDecimal.valueOf(4.5));

        pageable = PageRequest.of(0, 10);
    }

    @Test
    void getAllCoaches_WithoutFilter_ShouldReturnBasicCoaches() {
        // Given
        Page<Coach> coachPage = new PageImpl<>(Collections.singletonList(testCoach));
        when(coachRepository.findAllBasic(pageable)).thenReturn(coachPage);
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Page<CoachDTO> result = coachService.getAllCoaches(null, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(testCoachDTO, result.getContent().get(0));
        verify(coachRepository).findAllBasic(pageable);
        verify(coachMapper).toBasicDTO(testCoach);
    }

    @Test
    void getAllCoaches_WithFilter_ShouldUseSpecification() {
        // Given
        CoachFilterDTO filter = new CoachFilterDTO();
        filter.setSearchQuery("test");
        
        Page<Coach> coachPage = new PageImpl<>(Collections.singletonList(testCoach));
        when(coachRepository.findAll(any(org.springframework.data.jpa.domain.Specification.class), eq(pageable)))
            .thenReturn(coachPage);
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Page<CoachDTO> result = coachService.getAllCoaches(filter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(coachRepository).findAll(any(org.springframework.data.jpa.domain.Specification.class), eq(pageable));
    }

    @Test
    void getCoachById_WhenExists_ShouldReturnFullDTO() {
        // Given
        when(coachRepository.findByIdWithDetails("test-coach-id")).thenReturn(Optional.of(testCoach));
        when(coachMapper.toFullDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Optional<CoachDTO> result = coachService.getCoachById("test-coach-id");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testCoachDTO, result.get());
        verify(coachRepository).findByIdWithDetails("test-coach-id");
        verify(coachMapper).toFullDTO(testCoach);
    }

    @Test
    void getCoachById_WhenNotExists_ShouldReturnEmpty() {
        // Given
        when(coachRepository.findByIdWithDetails("non-existent-id")).thenReturn(Optional.empty());

        // When
        Optional<CoachDTO> result = coachService.getCoachById("non-existent-id");

        // Then
        assertFalse(result.isPresent());
        verify(coachRepository).findByIdWithDetails("non-existent-id");
        verify(coachMapper, never()).toFullDTO(any());
    }

    @Test
    void getCoachBasicById_WhenExists_ShouldReturnBasicDTO() {
        // Given
        when(coachRepository.findById("test-coach-id")).thenReturn(Optional.of(testCoach));
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Optional<CoachDTO> result = coachService.getCoachBasicById("test-coach-id");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testCoachDTO, result.get());
        verify(coachRepository).findById("test-coach-id");
        verify(coachMapper).toBasicDTO(testCoach);
    }

    @Test
    void searchCoaches_ShouldReturnMatchingCoaches() {
        // Given
        String searchQuery = "fitness";
        Page<Coach> coachPage = new PageImpl<>(Collections.singletonList(testCoach));
        when(coachRepository.searchCoaches(searchQuery, pageable)).thenReturn(coachPage);
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Page<CoachDTO> result = coachService.searchCoaches(searchQuery, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(coachRepository).searchCoaches(searchQuery, pageable);
    }

    @Test
    void getTopRatedCoaches_ShouldReturnTopRatedCoaches() {
        // Given
        Page<Coach> coachPage = new PageImpl<>(Collections.singletonList(testCoach));
        when(coachRepository.findTopRatedCoaches(pageable)).thenReturn(coachPage);
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        Page<CoachDTO> result = coachService.getTopRatedCoaches(pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(coachRepository).findTopRatedCoaches(pageable);
    }

    @Test
    void createCoach_ShouldCreateAndReturnCoach() {
        // Given
        when(coachMapper.toEntity(testCoachDTO)).thenReturn(testCoach);
        when(coachRepository.save(testCoach)).thenReturn(testCoach);
        when(coachMapper.toBasicDTO(testCoach)).thenReturn(testCoachDTO);

        // When
        CoachDTO result = coachService.createCoach(testCoachDTO);

        // Then
        assertNotNull(result);
        assertEquals(testCoachDTO, result);
        verify(coachMapper).toEntity(testCoachDTO);
        verify(coachRepository).save(testCoach);
        verify(coachMapper).toBasicDTO(testCoach);
    }

    @Test
    void deleteCoach_WhenExists_ShouldMarkAsDeleted() {
        // Given
        when(coachRepository.findById("test-coach-id")).thenReturn(Optional.of(testCoach));
        when(coachRepository.save(testCoach)).thenReturn(testCoach);

        // When
        boolean result = coachService.deleteCoach("test-coach-id");

        // Then
        assertTrue(result);
        assertTrue(testCoach.getDeleted());
        verify(coachRepository).findById("test-coach-id");
        verify(coachRepository).save(testCoach);
    }

    @Test
    void deleteCoach_WhenNotExists_ShouldReturnFalse() {
        // Given
        when(coachRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // When
        boolean result = coachService.deleteCoach("non-existent-id");

        // Then
        assertFalse(result);
        verify(coachRepository).findById("non-existent-id");
        verify(coachRepository, never()).save(any());
    }
}
