/*
 * Copyright (c) 2014 Irian Software Development SRL. All Rights Reserved.
 * This software is the proprietary information of Irian Software Development SRL.
 * Use is subject to license and non-disclosure terms
 */

package com.nutrition.libs.shared.domain.model;



import com.nutrition.libs.shared.domain.user.UserContextHolder;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Papp
 */
@MappedSuperclass

public abstract class MutableEntity extends BaseEntity {

  @SuppressWarnings({"UnusedDeclaration", "FieldCanBeLocal"})
  @Column(name = "last_modified_by", length = 35)
  private String lastModifiedBy;

  @Column(name = "last_modified_at")
  private LocalDateTime lastModifiedAt;


  @Column(name = "deleted")
  private boolean deleted = false;

  protected MutableEntity() {

  }

  public String getLastModifiedBy() {
    return lastModifiedBy;
  }

  public void setLastModifiedBy(String lastModifiedBy) {
    this.lastModifiedBy = lastModifiedBy;
  }

  public LocalDateTime getLastModifiedAt() {
    return lastModifiedAt;
  }

  public void setLastModifiedAt(LocalDateTime lastModifiedAt) {
    this.lastModifiedAt = lastModifiedAt;
  }

  @PrePersist
  public void prePersist() {
    super.prePersist();
    updateLastModified();
  }

  @PreUpdate
  protected void preUpdate() {
    updateLastModified();
  }


  private void updateLastModified() {
    lastModifiedBy = UserContextHolder.getUserContext().getUsername();
    this.lastModifiedAt =  LocalDateTime.now();
  }

  public boolean isDeleted() {
    return deleted;
  }

  public void setDeleted(boolean deleted) {
    this.deleted = deleted;
  }
}

