<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nutrition</groupId>
        <artifactId>nutrition-master</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../../pom.xml</relativePath> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.nutrition</groupId>
    <artifactId>nutrition-web</artifactId>
    <name>nutrition-web</name>
    <description>Project nutrition</description>
    <properties>
        <java.version>22</java.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Video Processing Dependencies -->
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-core</artifactId>
            <version>3.3.1</version>
        </dependency>
        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-nativebin-win64</artifactId>
            <version>3.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--	<dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
            </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.nutrition</groupId>
            <artifactId>tazz-crawler</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nutrition</groupId>
            <artifactId>products</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nutrition</groupId>
            <artifactId>jobs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nutrition</groupId>
            <artifactId>security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nutrition</groupId>
            <artifactId>user-profile</artifactId>
        </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>questionnaire</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>concepts</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>eatntrack-crawler</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>shared</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>video</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>photo</artifactId>
      </dependency>
      <dependency>
        <groupId>com.nutrition</groupId>
        <artifactId>coaches</artifactId>
      </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.nutrition.nutrition_web.NutritionWebApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
