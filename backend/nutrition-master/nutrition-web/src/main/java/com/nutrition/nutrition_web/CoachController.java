package com.nutrition.nutrition_web;

import com.nutrition.libs.coaches.service.CoachService;
import com.nutrition.libs.coaches.service.dto.CoachDTO;
import com.nutrition.libs.coaches.service.dto.CoachFilterDTO;
import com.nutrition.libs.coaches.service.dto.CoachReviewDTO;
import com.nutrition.libs.shared.web.dto.PagedResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Coach management
 * Provides endpoints for CRUD operations, filtering, and sorting with lazy loading
 */
@RestController
@RequestMapping("/api/coaches")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CoachController {

    @Autowired
    private CoachService coachService;

    /**
     * Get all coaches with pagination, filtering, and sorting
     *
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param sortDir Sort direction (asc/desc)
     * @param name Filter by name (partial match)
     * @param location Filter by location
     * @param specialties Filter by specialties (comma-separated)
     * @param environments Filter by environments (comma-separated)
     * @param targetGoals Filter by target goals (comma-separated)
     * @param minRating Minimum rating filter
     * @param maxPrice Maximum price filter
     * @param isPro Filter by pro status
     * @param languages Filter by languages (comma-separated)
     * @return Paged response with coaches
     */
    @GetMapping
    public ResponseEntity<PagedResponse<CoachDTO>> getAllCoaches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String specialties,
            @RequestParam(required = false) String environments,
            @RequestParam(required = false) String targetGoals,
            @RequestParam(required = false) BigDecimal minRating,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) Boolean isPro,
            @RequestParam(required = false) String languages) {

        // Create sort object
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        // Convert comma-separated strings to lists
        List<String> specialtyList = specialties != null ?
            List.of(specialties.split(",")) : null;
        List<String> environmentList = environments != null ?
            List.of(environments.split(",")) : null;
        List<String> targetGoalList = targetGoals != null ?
            List.of(targetGoals.split(",")) : null;
        List<String> languageList = languages != null ?
            List.of(languages.split(",")) : null;

        // Create filter DTO
        CoachFilterDTO filter = new CoachFilterDTO();
        filter.setSearchQuery(name);
        filter.setLocation(location);
        filter.setSpecialties(specialtyList);
        filter.setTargetGoals(targetGoalList);
        filter.setEnvironments(environmentList);
        filter.setMinPrice(minRating);
        filter.setMaxPrice(maxPrice);
        filter.setIsPro(isPro);
        filter.setLanguages(languageList);

        Page<CoachDTO> coaches = coachService.getAllCoaches(filter, pageable);

        PagedResponse<CoachDTO> response = new PagedResponse<>(
            coaches.getContent(),
            coaches.getNumber(),
            coaches.getSize(),
            coaches.getTotalElements(),
            coaches.getTotalPages(),
            coaches.isFirst(),
            coaches.isLast()
        );

        return ResponseEntity.ok(response);
    }

    /**
     * Get coach by ID with full details
     *
     * @param id Coach ID
     * @return Coach details
     */
    @GetMapping("/{id}")
    public ResponseEntity<CoachDTO> getCoachById(@PathVariable String id) {
        Optional<CoachDTO> coach = coachService.getCoachBasicById(id);
        return coach.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get coach with full details including reviews, certifications, etc.
     *
     * @param id Coach ID
     * @return Coach with full details
     */
    @GetMapping("/{id}/details")
    public ResponseEntity<CoachDTO> getCoachWithDetails(@PathVariable String id) {
        Optional<CoachDTO> coach = coachService.getCoachById(id);
        return coach.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create a new coach
     *
     * @param coachDTO Coach data
     * @return Created coach
     */
    @PostMapping
    public ResponseEntity<CoachDTO> createCoach(@Valid @RequestBody CoachDTO coachDTO) {
        CoachDTO createdCoach = coachService.createCoach(coachDTO);
        return ResponseEntity.ok(createdCoach);
    }

    /**
     * Update an existing coach
     *
     * @param id Coach ID
     * @param coachDTO Updated coach data
     * @return Updated coach
     */
    @PutMapping("/{id}")
    public ResponseEntity<CoachDTO> updateCoach(
            @PathVariable String id,
            @Valid @RequestBody CoachDTO coachDTO) {
        Optional<CoachDTO> updatedCoach = coachService.updateCoach(id, coachDTO);
        return updatedCoach.map(ResponseEntity::ok)
                          .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Delete a coach (soft delete)
     *
     * @param id Coach ID
     * @return Success status
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCoach(@PathVariable String id) {
        boolean deleted = coachService.deleteCoach(id);
        return deleted ? ResponseEntity.noContent().build()
                      : ResponseEntity.notFound().build();
    }

    /**
     * Get reviews for a specific coach
     *
     * @param id Coach ID
     * @param page Page number
     * @param size Page size
     * @return Paged reviews
     */
    @GetMapping("/{id}/reviews")
    public ResponseEntity<PagedResponse<CoachReviewDTO>> getCoachReviews(
            @PathVariable String id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size,
            Sort.by(Sort.Direction.DESC, "reviewDate"));

        Page<CoachReviewDTO> reviews = coachService.getCoachReviews(id, pageable);

        PagedResponse<CoachReviewDTO> response = new PagedResponse<>(
            reviews.getContent(),
            reviews.getNumber(),
            reviews.getSize(),
            reviews.getTotalElements(),
            reviews.getTotalPages(),
            reviews.isFirst(),
            reviews.isLast()
        );

        return ResponseEntity.ok(response);
    }

    /**
     * Add a review for a coach
     *
     * @param id Coach ID
     * @param reviewDTO Review data
     * @return Created review
     */
    @PostMapping("/{id}/reviews")
    public ResponseEntity<CoachReviewDTO> addCoachReview(
            @PathVariable String id,
            @Valid @RequestBody CoachReviewDTO reviewDTO) {
        CoachReviewDTO createdReview = coachService.addReview(id, reviewDTO);
        return ResponseEntity.ok(createdReview);
    }

    /**
     * Get available filter options
     *
     * @return Filter options
     */
    @GetMapping("/filter-options")
    public ResponseEntity<CoachService.CoachFilterOptionsDTO> getFilterOptions() {
        CoachService.CoachFilterOptionsDTO filterOptions = coachService.getFilterOptions();
        return ResponseEntity.ok(filterOptions);
    }

    /**
     * Get available specialties for filtering
     *
     * @return List of specialties
     */
    @GetMapping("/specialties")
    public ResponseEntity<List<String>> getAvailableSpecialties() {
        CoachService.CoachFilterOptionsDTO filterOptions = coachService.getFilterOptions();
        return ResponseEntity.ok(filterOptions.getSpecialties());
    }

    /**
     * Get available environments for filtering
     *
     * @return List of environments
     */
    @GetMapping("/environments")
    public ResponseEntity<List<String>> getAvailableEnvironments() {
        CoachService.CoachFilterOptionsDTO filterOptions = coachService.getFilterOptions();
        return ResponseEntity.ok(filterOptions.getEnvironments());
    }

    /**
     * Get available target goals for filtering
     *
     * @return List of target goals
     */
    @GetMapping("/target-goals")
    public ResponseEntity<List<String>> getAvailableTargetGoals() {
        CoachService.CoachFilterOptionsDTO filterOptions = coachService.getFilterOptions();
        return ResponseEntity.ok(filterOptions.getTargetGoals());
    }

    /**
     * Get available languages for filtering
     *
     * @return List of languages
     */
    @GetMapping("/languages")
    public ResponseEntity<List<String>> getAvailableLanguages() {
        CoachService.CoachFilterOptionsDTO filterOptions = coachService.getFilterOptions();
        return ResponseEntity.ok(filterOptions.getLanguages());
    }
}
