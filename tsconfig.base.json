{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@nutrition/auth": ["libs/auth/src/index.ts"], "@nutrition/camera-test": ["libs/playground/src/lib/playground/camera-test/src/index.ts"], "@nutrition/coaches": ["libs/coaches/src/index.ts"], "@nutrition/commons": ["libs/commons/src/index.ts"], "@nutrition/experts": ["libs/experts/src/index.ts"], "@nutrition/explorer": ["libs/explorer/src/index.ts"], "@nutrition/home": ["libs/home/<USER>/index.ts"], "@nutrition/journal": ["libs/journal/src/index.ts"], "@nutrition/plans": ["libs/plans/src/index.ts"], "@nutrition/playground": ["libs/playground/src/index.ts"], "@nutrition/shared": ["libs/shared/src/index.ts"], "@nutrition/shop": ["libs/shop/src/index.ts"], "@nutrition/user-profile": ["libs/user-profile/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}